import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import type { ProjectKPI } from '../../../../shared/types'

interface CreateKPIDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (kpiData: Omit<ProjectKPI, 'id' | 'updatedAt'>) => Promise<void>
  initialData?: ProjectKPI | null
  projectId: string
}

// Common KPI templates for quick setup
const KPI_TEMPLATES = [
  { name: 'Bug修复数', unit: '个', target: '10' },
  { name: '新增用户', unit: '人', target: '100' },
  { name: '完成率', unit: '%', target: '100' },
  { name: '代码覆盖率', unit: '%', target: '80' },
  { name: '响应时间', unit: 'ms', target: '200' },
  { name: '用户满意度', unit: '分', target: '4.5' },
  { name: '销售额', unit: '元', target: '10000' },
  { name: '页面访问量', unit: '次', target: '1000' }
]

export function CreateKPIDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  projectId
}: CreateKPIDialogProps) {
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    value: initialData?.value || '0',
    target: initialData?.target || '',
    unit: initialData?.unit || '',
    frequency: initialData?.frequency || 'daily'
  })
  const [isCustomUnit, setIsCustomUnit] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      return
    }

    setIsSubmitting(true)
    try {
      const kpiData = {
        projectId,
        name: formData.name.trim(),
        value: formData.value.trim() || '0',
        target: formData.target.trim() || undefined,
        unit: formData.unit.trim() || undefined,
        frequency: formData.frequency
      }

      await onSubmit(kpiData)
      onClose()

      // Reset form
      setFormData({
        name: '',
        value: '0',
        target: '',
        unit: '',
        frequency: 'daily'
      })
    } catch (error) {
      console.error('Failed to create/update KPI:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const handleTemplateSelect = (template: typeof KPI_TEMPLATES[0]) => {
    setFormData({
      name: template.name,
      value: '0',
      target: template.target,
      unit: template.unit
    })
  }

  const isEditing = !!initialData

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit KPI' : 'Create New KPI'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the key performance indicator for this project.'
              : 'Add a key performance indicator to track project success metrics.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* KPI Templates (only show when creating) */}
          {!isEditing && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Quick Templates</Label>
              <div className="grid grid-cols-2 gap-2">
                {KPI_TEMPLATES.map((template, index) => (
                  <Button
                    key={index}
                    type="button"
                    variant="outline"
                    size="sm"
                    className="justify-start text-xs h-8"
                    onClick={() => handleTemplateSelect(template)}
                  >
                    {template.name}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* KPI Name */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium">
              KPI Name *
            </Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g., Bug修复数, 新增用户, 完成率"
              required
            />
          </div>

          {/* Current Value and Target */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="value" className="text-sm font-medium">
                Current Value *
              </Label>
              <Input
                id="value"
                value={formData.value}
                onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                placeholder="0"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="target" className="text-sm font-medium">
                Target Value
              </Label>
              <Input
                id="target"
                value={formData.target}
                onChange={(e) => setFormData({ ...formData, target: e.target.value })}
                placeholder="100"
              />
            </div>
          </div>

          {/* Unit */}
          <div className="space-y-2">
            <Label htmlFor="unit" className="text-sm font-medium">
              Unit
            </Label>
            <Select
              value={isCustomUnit ? 'custom' : (formData.unit || 'none')}
              onValueChange={(value) => {
                if (value === 'custom') {
                  setIsCustomUnit(true)
                  setFormData({ ...formData, unit: '' })
                } else if (value === 'none') {
                  setIsCustomUnit(false)
                  setFormData({ ...formData, unit: '' })
                } else {
                  setIsCustomUnit(false)
                  setFormData({ ...formData, unit: value })
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select unit or leave empty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No unit</SelectItem>
                <SelectItem value="个">个</SelectItem>
                <SelectItem value="人">人</SelectItem>
                <SelectItem value="%">%</SelectItem>
                <SelectItem value="分">分</SelectItem>
                <SelectItem value="元">元</SelectItem>
                <SelectItem value="次">次</SelectItem>
                <SelectItem value="天">天</SelectItem>
                <SelectItem value="小时">小时</SelectItem>
                <SelectItem value="ms">ms</SelectItem>
                <SelectItem value="MB">MB</SelectItem>
                <SelectItem value="custom">Custom...</SelectItem>
              </SelectContent>
            </Select>
            {/* Custom unit input - show when custom is selected */}
            {isCustomUnit && (
              <Input
                value={formData.unit}
                onChange={(e) => setFormData({ ...formData, unit: e.target.value })}
                placeholder="Enter custom unit"
                className="mt-2"
              />
            )}
          </div>

          {/* Recording Frequency */}
          <div className="space-y-2">
            <Label htmlFor="frequency">Recording Frequency</Label>
            <Select
              value={formData.frequency}
              onValueChange={(value) => setFormData({ ...formData, frequency: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select recording frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="as-needed">As Needed</SelectItem>
              </SelectContent>
            </Select>
            <div className="text-xs text-muted-foreground">
              How often do you plan to record data for this KPI?
            </div>
          </div>

          {/* Preview */}
          {formData.name && (
            <div className="p-3 bg-muted/50 rounded-lg border">
              <div className="text-sm font-medium text-muted-foreground mb-1">Preview:</div>
              <div className="flex items-center gap-2">
                <span className="font-medium">{formData.name}:</span>
                <span className="text-lg font-semibold text-primary">
                  {formData.value}{formData.unit && ` ${formData.unit}`}
                </span>
                {formData.target && (
                  <>
                    <span className="text-muted-foreground">/</span>
                    <span className="text-muted-foreground">
                      {formData.target}{formData.unit && ` ${formData.unit}`}
                    </span>
                  </>
                )}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || !formData.name.trim()}>
              {isSubmitting ? 'Saving...' : isEditing ? 'Update KPI' : 'Create KPI'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateKPIDialog
