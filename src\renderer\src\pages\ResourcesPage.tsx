import { Outlet, useLocation } from 'react-router-dom'
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { PageHeader, PageHeaderActions } from '../components/shared'
import { Button } from '../components/ui/button'
import FileTree from '../components/features/FileTree'
import MarkdownEditor from '../components/features/MarkdownEditor'

import {
  BidirectionalBacklinks,
  BidirectionalOutlinks,
  LinkStatisticsCard
} from '../components/features/BidirectionalLinks'
import { useLanguage } from '../contexts/LanguageContext'

import type { FileTreeItem } from '../components/features/FileTreeNode'
import { fileSystemApi, databaseApi } from '../lib/api'
import { useUIStore } from '../store/uiStore'
import { useResourceStore } from '../store/resourceStore'
import { useUserSettingsStore } from '../store/userSettingsStore'
import { DeleteConfirmDialog } from '../components/features/DeleteConfirmDialog'
import { useConfirmDialog } from '../components/shared/ConfirmDialog'
import { getResourcesPath, createPage, normalizePageName } from '../plugins/wikilink/utils'
import { cn } from '../lib/utils'

// 简单的路径拼接函数，用于替代Node.js的path.join
const joinPath = (...parts: string[]): string => {
  return parts
    .map((part) => part.replace(/^\/+|\/+$/g, '')) // 移除开头和结尾的斜杠
    .filter((part) => part.length > 0)
    .join('/')
}

// 将虚拟路径转换为文件系统路径
const convertToRealPath = async (virtualPath: string, userSettings?: any): Promise<string> => {
  let basePath: string

  if (userSettings?.workspaceDirectory) {
    // 使用用户设置的工作目录
    basePath = joinPath(userSettings.workspaceDirectory, 'PaoLife')
  } else {
    // 使用默认的用户数据目录
    const userDataPath = await window.electronAPI.app.getPath('userData')
    basePath = joinPath(userDataPath, 'resources')
  }

  // 移除开头的斜杠，构建相对路径
  const relativePath = virtualPath.replace(/^\/+/, '')

  // 如果是根路径，直接返回基础目录
  if (!relativePath) {
    return basePath
  }

  // 构建完整路径：基础目录/相对路径
  return joinPath(basePath, relativePath)
}

export function ResourcesPage() {
  const location = useLocation()
  const isDetailView = location.pathname !== '/resources'
  const [selectedFile, setSelectedFile] = useState<FileTreeItem | null>(null)
  const [showBacklinks, setShowBacklinks] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [bidirectionalBacklinks, setBidirectionalBacklinks] = useState<any[]>([])
  const [bidirectionalOutlinks, setBidirectionalOutlinks] = useState<any[]>([])
  const [linkStatistics, setLinkStatistics] = useState<any>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<FileTreeItem | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // 编辑器相关状态
  const [currentFileContent, setCurrentFileContent] = useState<string>('')
  const [isEditorReady, setIsEditorReady] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [currentEditorContent, setCurrentEditorContent] = useState<string>('')

  const { t } = useLanguage()
  const { addNotification, setLoadingState, clearLoadingState } = useUIStore()
  const { addResource } = useResourceStore()
  const { settings, setFocusMode } = useUserSettingsStore()
  const { confirm, ConfirmDialog: CreateFileConfirmDialog } = useConfirmDialog()
  // const { openFile } = useEditorStore() // Removed

  // 专注模式状态
  const isFocusMode = settings.focusMode || false

  // 处理编辑器内容变化
  const handleEditorChange = useCallback((markdown: string) => {
    setCurrentEditorContent(markdown)
    setHasUnsavedChanges(true)
    // 这里可以添加自动保存逻辑
  }, [])

  // 解析页面路径
  const resolvePagePath = useCallback(async (pageName: string): Promise<string> => {
    const resourcesPath = await getResourcesPath(settings)
    const normalizedName = normalizePageName(pageName)
    return `${resourcesPath}/${normalizedName}.md`
  }, [settings])

  // 打开现有文件
  const openExistingFile = useCallback(async (filePath: string) => {
    try {
      const result = await fileSystemApi.readFile({ path: filePath })
      if (result.success && result.data) {
        const content = result.data.content || ''
        setCurrentFileContent(content)
        setCurrentEditorContent(content)
        setHasUnsavedChanges(false)

        // 创建一个虚拟的 FileTreeItem 来更新选中状态
        const fileName = filePath.split('/').pop() || ''
        const virtualPath = filePath.replace(await getResourcesPath(settings), '').replace(/^\//, '')
        const virtualFile: FileTreeItem = {
          id: `wikilink-${Date.now()}`,
          name: fileName,
          path: `/${virtualPath}`,
          type: 'file',
          children: [],
          modifiedAt: new Date().toISOString(),
          createdAt: new Date().toISOString()
        }
        setSelectedFile(virtualFile)

        addNotification({
          type: 'success',
          title: 'WikiLink 跳转成功',
          message: `已打开文件：${fileName}`
        })
      } else {
        throw new Error(result.error || '无法读取文件内容')
      }
    } catch (error) {
      console.error('打开文件失败:', error)
      addNotification({
        type: 'error',
        title: '打开文件失败',
        message: error instanceof Error ? error.message : '打开文件时发生未知错误'
      })
    }
  }, [settings, addNotification])

  // 创建并打开新文件
  const createAndOpenNewFile = useCallback(async (filePath: string, pageName: string) => {
    try {
      const resourcesPath = await getResourcesPath(settings)
      const normalizedName = normalizePageName(pageName)
      const defaultContent = `# ${pageName}\n\n创建时间：${new Date().toLocaleString()}\n`

      const success = await createPage(normalizedName, resourcesPath, defaultContent)
      if (success) {
        await openExistingFile(filePath)
        addNotification({
          type: 'success',
          title: '文件创建成功',
          message: `文件 "${pageName}.md" 已创建并打开`
        })
      } else {
        throw new Error('创建文件失败')
      }
    } catch (error) {
      console.error('创建文件失败:', error)
      addNotification({
        type: 'error',
        title: '创建文件失败',
        message: error instanceof Error ? error.message : '创建文件时发生未知错误'
      })
    }
  }, [settings, addNotification, openExistingFile])

  // WikiLink 智能跳转处理
  const handleWikiLinkClick = useCallback(async (pageName: string) => {
    try {
      const filePath = await resolvePagePath(pageName)
      const existsResult = await fileSystemApi.fileExists(filePath)

      if (existsResult.success && existsResult.data) {
        // 文件存在，直接打开
        await openExistingFile(filePath)
      } else {
        // 文件不存在，显示创建确认对话框
        confirm({
          title: '创建新文件',
          description: `文件 "${pageName}.md" 不存在，是否要创建这个新文件？`,
          variant: 'default',
          confirmText: '创建文件',
          cancelText: '取消',
          onConfirm: async () => {
            await createAndOpenNewFile(filePath, pageName)
          }
        })
      }
    } catch (error) {
      console.error('WikiLink 跳转失败:', error)
      addNotification({
        type: 'error',
        title: 'WikiLink 跳转失败',
        message: error instanceof Error ? error.message : '跳转时发生未知错误'
      })
    }
  }, [resolvePagePath, openExistingFile, createAndOpenNewFile, confirm, addNotification])

  // WikiLink 配置
  const wikiLinkConfig = useMemo(() => {
    console.log('📋 [DEBUG] 创建 WikiLink 配置，当前 settings:', settings)

    const config = {
      enableAutoComplete: false,  // 自动补全功能已移除
      enablePreview: true,  // 启用预览功能
      enableBidirectionalLinks: true,
      previewMaxLines: 5,
      autoCreatePages: false,
      readPageContent: async (pageName: string) => {
        console.log('📋 [DEBUG] readPageContent 被调用，页面名称:', pageName)
        try {
          const filePath = await resolvePagePath(pageName)
          console.log('📋 [DEBUG] 解析的文件路径:', filePath)

          // 总是读取文件的最新内容，确保预览显示最新状态
          // 移除了直接返回编辑器内容的逻辑，以确保预览始终显示文件的最新保存状态

          // 尝试读取文件，添加重试机制
          let retries = 2
          while (retries > 0) {
            try {
              const result = await fileSystemApi.readFile({ path: filePath })
              if (result.success && result.data) {
                const content = result.data.content || ''
                console.log('📋 [DEBUG] 读取到的完整内容长度:', content.length)
                console.log('📋 [DEBUG] 内容前100字符:', content.substring(0, 100))
                // 返回完整内容，让预览编辑器自己处理显示
                return content
              }
              console.warn('⚠️ [DEBUG] 文件读取失败或无内容')
              return ''
            } catch (fileError: any) {
              retries--
              if (retries > 0 && fileError.message?.includes('locked')) {
                console.log('📋 [DEBUG] 文件被锁定，等待重试...')
                await new Promise(resolve => setTimeout(resolve, 100))
                continue
              }
              throw fileError
            }
          }
          return ''
        } catch (error) {
          console.error('❌ [DEBUG] 读取页面内容失败:', error)
          return `预览加载失败: ${pageName}`
        }
      },
      savePageContent: async (pageName: string, content: string) => {
        console.log('📋 [DEBUG] savePageContent 被调用，页面名称:', pageName)
        try {
          const filePath = await resolvePagePath(pageName)
          console.log('📋 [DEBUG] 保存文件路径:', filePath)

          const result = await fileSystemApi.writeFile({
            path: filePath,
            content: content
          })

          if (result.success) {
            console.log('📋 [DEBUG] 文件保存成功')

            // 如果保存的是当前打开的文件，更新编辑器内容
            if (selectedFile && selectedFile.path === filePath) {
              setCurrentEditorContent(content)
            }

            // 显示保存成功通知
            addNotification({
              type: 'success',
              title: '保存成功',
              message: `文件 "${pageName}" 已保存`
            })
          } else {
            throw new Error(result.error || '保存失败')
          }
        } catch (error) {
          console.error('❌ [DEBUG] 保存页面内容失败:', error)
          addNotification({
            type: 'error',
            title: '保存失败',
            message: error instanceof Error ? error.message : '保存时发生未知错误'
          })
          throw error
        }
      }
    }

    console.log('📋 [DEBUG] WikiLink 配置创建完成:', {
      enableAutoComplete: config.enableAutoComplete,
      enablePreview: config.enablePreview,
      hasReadPageContent: !!config.readPageContent
    })

    return config
  }, [settings, resolvePagePath, selectedFile, currentEditorContent, addNotification, setCurrentEditorContent])

  // 保存当前文件
  const handleSaveFile = useCallback(async (content: string) => {
    if (!selectedFile || selectedFile.type !== 'file') {
      addNotification({
        type: 'warning',
        title: '无法保存',
        message: '请先选择一个文件'
      })
      return
    }

    const loadingKey = 'file-save'
    setLoadingState(loadingKey, true)

    try {
      // 将虚拟路径转换为真实路径
      const realPath = await convertToRealPath(selectedFile.path, settings)

      // 保存文件内容
      const result = await fileSystemApi.writeFile({
        path: realPath,
        content: content,
        createDirs: false
      })

      if (result.success) {
        setHasUnsavedChanges(false)
        addNotification({
          type: 'success',
          title: '保存成功',
          message: `文件 "${selectedFile.name}" 已保存`
        })
      } else {
        addNotification({
          type: 'error',
          title: '保存失败',
          message: result.error || '保存文件时发生错误'
        })
      }
    } catch (error) {
      console.error('保存文件失败:', error)
      addNotification({
        type: 'error',
        title: '保存失败',
        message: error instanceof Error ? error.message : '保存文件时发生未知错误'
      })
    } finally {
      clearLoadingState(loadingKey)
    }
  }, [selectedFile, settings, addNotification, setLoadingState, clearLoadingState])

  // 加载双向链接数据
  const loadBidirectionalLinks = useCallback(async () => {
    if (!selectedFile || selectedFile.type !== 'file') {
      setBidirectionalBacklinks([])
      setBidirectionalOutlinks([])
      setLinkStatistics(null)
      return
    }

    // 暂时设置为空数组，因为编辑器功能已移除
    setBidirectionalBacklinks([])
    setBidirectionalOutlinks([])
    setLinkStatistics(null)
  }, [selectedFile])

  // 检查是否需要自动打开指定的资源文件
  useEffect(() => {
    const openResourcePath = sessionStorage.getItem('openResourcePath')
    if (openResourcePath) {
      // 清除 sessionStorage
      sessionStorage.removeItem('openResourcePath')

      // 自动打开文件
      openExistingFile(openResourcePath)

      console.log(`🔗 [自动打开] 打开资源文件: ${openResourcePath}`)
    }
  }, [openExistingFile])

  // 当选中文件变化时加载双向链接
  useEffect(() => {
    loadBidirectionalLinks()
  }, [selectedFile, loadBidirectionalLinks])

  // 专注模式切换函数
  const toggleFocusMode = useCallback((): void => {
    setFocusMode(!isFocusMode)
  }, [isFocusMode, setFocusMode])

  // 键盘快捷键支持
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent): void => {
      // F11 或 Ctrl+Shift+F 切换专注模式
      if (event.key === 'F11' || (event.ctrlKey && event.shiftKey && event.key === 'F')) {
        event.preventDefault()
        toggleFocusMode()
      }

      // Ctrl+S 保存文件
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault()
        if (selectedFile && selectedFile.type === 'file' && selectedFile.name.endsWith('.md') && hasUnsavedChanges) {
          handleSaveFile(currentEditorContent)
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isFocusMode, toggleFocusMode, selectedFile, hasUnsavedChanges, currentEditorContent, handleSaveFile])

  if (isDetailView) {
    return <Outlet />
  }

  const handleFileSelect = async (file: FileTreeItem) => {
    setSelectedFile(file)
    console.log('Selected file:', file)

    // 如果是Markdown文件，在编辑器中打开
    if (file.type === 'file' && file.name.endsWith('.md')) {
      try {
        // 将虚拟路径转换为真实路径
        const realPath = await convertToRealPath(file.path, settings)

        // 读取文件内容
        const result = await fileSystemApi.readFile({ path: realPath })
        console.log('已读取文件内容:', result.data?.content)
        if (result.success && result.data) {
          // 设置编辑器内容
          const content = result.data.content || ''
          setCurrentFileContent(content)
          setCurrentEditorContent(content) // 同步编辑器内容状态
          setHasUnsavedChanges(false)
          console.log('文件已加载到编辑器:', file.name)
        } else {
          addNotification({
            type: 'error',
            title: '读取文件失败',
            message: result.error || '无法读取文件内容'
          })
        }
      } catch (error) {
        console.error('打开文件失败:', error)
        addNotification({
          type: 'error',
          title: '打开文件失败',
          message: error instanceof Error ? error.message : '打开文件时发生未知错误'
        })
      }
    } else {
      // 如果不是Markdown文件，清空编辑器
      setCurrentFileContent('')
      setCurrentEditorContent('')
      setHasUnsavedChanges(false)
    }
  }

  const handleFileCreate = async (parentPath: string, name: string, type: 'file' | 'folder') => {
    const loadingKey = 'file-create'
    setLoadingState(loadingKey, true)

    try {
      // 将虚拟路径转换为真实的文件系统路径
      const realParentPath = await convertToRealPath(parentPath, settings)

      if (type === 'file') {
        // 确保文件有.md扩展名（如果没有的话）
        const fileName = name.endsWith('.md') ? name : `${name}.md`
        const filePath = joinPath(realParentPath, fileName)

        // 检查文件是否已存在
        const existsResult = await fileSystemApi.fileExists(filePath)
        if (existsResult.success && existsResult.data) {
          addNotification({
            type: 'warning',
            title: '文件已存在',
            message: `文件 "${fileName}" 已存在，请使用其他名称`
          })
          return
        }

        const defaultContent = `# ${name}\n\n创建时间：${new Date().toLocaleString()}\n`
        await fileSystemApi.writeFile({
          path: filePath,
          content: defaultContent,
          createDirs: true
        })

        addNotification({
          type: 'success',
          title: '文件创建成功',
          message: `文件 "${fileName}" 已创建`
        })

        // 编辑器已移除，仅记录文件创建
        console.log('文件已创建:', fileName)
      } else {
        const folderPath = joinPath(realParentPath, name)

        // 检查文件夹是否已存在
        const existsResult = await fileSystemApi.fileExists(folderPath)
        if (existsResult.success && existsResult.data) {
          addNotification({
            type: 'warning',
            title: '文件夹已存在',
            message: `文件夹 "${name}" 已存在，请使用其他名称`
          })
          return
        }

        await fileSystemApi.createDirectory(folderPath)

        addNotification({
          type: 'success',
          title: '文件夹创建成功',
          message: `文件夹 "${name}" 已创建`
        })
      }

      // 触发文件树刷新
      setRefreshTrigger((prev) => prev + 1)
    } catch (error) {
      console.error('Failed to create', type, ':', error)
      addNotification({
        type: 'error',
        title: '创建失败',
        message:
          error instanceof Error ? error.message : `创建${type === 'file' ? '文件' : '文件夹'}失败`
      })
    } finally {
      clearLoadingState(loadingKey)
    }
  }

  const handleAddResource = async () => {
    if (!selectedFile) {
      addNotification({
        type: 'warning',
        title: '请选择文件',
        message: '请先在左侧文件树中点击选择一个文件，然后再点击添加资源按钮'
      })
      return
    }

    // 检查是否是文件（不是文件夹）
    if (selectedFile.type !== 'file') {
      addNotification({
        type: 'warning',
        title: '请选择文件',
        message: '只能将文件添加到资源库，请选择一个文件而不是文件夹'
      })
      return
    }

    const loadingKey = 'add-resource'
    setLoadingState(loadingKey, true)

    try {
      const result = await databaseApi.createResource({
        resourcePath: selectedFile.path,
        title: selectedFile.name.replace('.md', ''), // 移除.md扩展名作为标题
        projectId: undefined, // 暂时不关联项目，后续可以扩展
        areaId: undefined // 暂时不关联领域，后续可以扩展
      })

      if (result.success) {
        // 更新resourceStore状态
        addResource(result.data)

        addNotification({
          type: 'success',
          title: '资源添加成功',
          message: `文件 "${selectedFile.name}" 已添加到资源库`
        })
      } else {
        throw new Error(result.error || '添加资源失败')
      }
    } catch (error) {
      console.error('Failed to add resource:', error)
      addNotification({
        type: 'error',
        title: '添加资源失败',
        message: error instanceof Error ? error.message : '添加资源时发生未知错误'
      })
    } finally {
      clearLoadingState(loadingKey)
    }
  }

  // 处理删除请求（显示确认对话框）
  const handleFileDelete = (item: FileTreeItem) => {
    setItemToDelete(item)
    setDeleteDialogOpen(true)
  }

  // 确认删除
  const handleDeleteConfirm = async () => {
    if (!itemToDelete) return

    setIsDeleting(true)

    try {
      // 将虚拟路径转换为真实路径
      const realPath = await convertToRealPath(itemToDelete.path, settings)
      console.log('Deleting:', realPath)

      let result: any
      if (itemToDelete.type === 'file') {
        result = await fileSystemApi.deleteFile(realPath)
      } else {
        result = await fileSystemApi.deleteDirectory(realPath)
      }

      if (result.success) {
        addNotification({
          type: 'success',
          title: '删除成功',
          message: `${itemToDelete.type === 'file' ? '文件' : '文件夹'} "${itemToDelete.name}" 已删除`
        })

        // 如果删除的是当前选中的文件，清除选择
        if (selectedFile && selectedFile.path === itemToDelete.path) {
          setSelectedFile(null)
        }

        // 刷新文件树
        setRefreshTrigger((prev) => prev + 1)
      } else {
        addNotification({
          type: 'error',
          title: '删除失败',
          message: result.error || '未知错误'
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '删除失败',
        message: '操作过程中发生错误'
      })
    } finally {
      setIsDeleting(false)
      setDeleteDialogOpen(false)
      setItemToDelete(null)
    }
  }

  // 取消删除
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
    setItemToDelete(null)
  }

  // 处理文件/文件夹重命名
  const handleFileRename = async (item: any, newName: string) => {
    const loadingKey = 'file-rename'
    setLoadingState(loadingKey, true)

    try {
      console.log('Renaming item:', item)
      console.log('New name:', newName)

      // 将虚拟路径转换为真实路径
      const oldRealPath = await convertToRealPath(item.path, settings)
      console.log('Old real path:', oldRealPath)

      // 构建新的路径
      const parentPath = item.path.substring(0, item.path.lastIndexOf('/'))
      const newVirtualPath = parentPath ? `${parentPath}/${newName}` : `/${newName}`
      const newRealPath = await convertToRealPath(newVirtualPath, settings)
      console.log('New real path:', newRealPath)

      // 如果是文件且没有扩展名，自动添加.md
      if (item.type === 'file' && !newName.includes('.')) {
        const finalNewName = `${newName}.md`
        const finalNewVirtualPath = parentPath
          ? `${parentPath}/${finalNewName}`
          : `/${finalNewName}`
        const finalNewRealPath = await convertToRealPath(finalNewVirtualPath, settings)

        const result = await fileSystemApi.rename(oldRealPath, finalNewRealPath)
        if (result.success) {
          addNotification({
            type: 'success',
            title: '重命名成功',
            message: `${item.type === 'file' ? '文件' : '文件夹'} "${item.name}" 已重命名为 "${finalNewName}"`
          })
          setRefreshTrigger((prev) => prev + 1)
        } else {
          addNotification({
            type: 'error',
            title: '重命名失败',
            message: result.error || '未知错误'
          })
        }
      } else {
        const result = await fileSystemApi.rename(oldRealPath, newRealPath)
        if (result.success) {
          addNotification({
            type: 'success',
            title: '重命名成功',
            message: `${item.type === 'file' ? '文件' : '文件夹'} "${item.name}" 已重命名为 "${newName}"`
          })
          setRefreshTrigger((prev) => prev + 1)
        } else {
          addNotification({
            type: 'error',
            title: '重命名失败',
            message: result.error || '未知错误'
          })
        }
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '重命名失败',
        message: '操作过程中发生错误'
      })
    } finally {
      clearLoadingState(loadingKey)
    }
  }

  return (
    <div
      className={cn(
        'container mx-auto p-6 space-y-6 focus-mode-transition',
        isFocusMode && 'focus-mode'
      )}
    >
      <PageHeader
        title={t('pages.resources.title')}
        description={t('pages.resources.description')}
        badge={{ text: 'P.A.R.A.', variant: 'secondary' }}
        actions={
          <div className="flex items-center gap-2">
            {/* 专注模式切换按钮 */}
            <Button
              variant={isFocusMode ? 'default' : 'outline'}
              size="sm"
              onClick={toggleFocusMode}
              className="flex items-center gap-2"
              title={isFocusMode ? '退出专注模式 (F11)' : '进入专注模式 (F11)'}
            >
              {isFocusMode ? (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
                    />
                  </svg>
                  退出专注
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 8l4-4m0 0h4M8 4v4m8-8l4 4m0 0v4m0-4h-4m4 8l-4 4m0 0h-4m4 0v-4M8 16l-4 4m0 0v-4m0 4h4"
                    />
                  </svg>
                  专注模式
                </>
              )}
            </Button>

            <PageHeaderActions.Create
              onClick={handleAddResource}
              disabled={!selectedFile || selectedFile.type !== 'file'}
            >
              {selectedFile && selectedFile.type === 'file'
                ? `添加 "${selectedFile.name}" 到资源库`
                : t('pages.resources.addResource')}
            </PageHeaderActions.Create>
          </div>
        }
        className="border-l-4 border-resource pl-6"
      />

      {/* File Tree, Editor, and Links Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-200px)]">
        {/* File Tree - 在专注模式下隐藏 */}
        {!isFocusMode && (
          <div className="lg:col-span-1">
            <FileTree
              onFileSelect={handleFileSelect}
              onFileCreate={handleFileCreate}
              onFileRename={handleFileRename}
              onFileDelete={handleFileDelete}
              onFileTreeChange={() => {}}
              refreshTrigger={refreshTrigger}
            />
          </div>
        )}

        {/* File Content Display Area */}
        <div className={cn(
          'flex flex-col',
          isFocusMode ? 'lg:col-span-4' : 'lg:col-span-2'
        )}>
          {selectedFile && selectedFile.type === 'file' && selectedFile.name.endsWith('.md') ? (
            <div className="flex flex-col h-full">
              {/* 文件信息栏 */}
              <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium text-gray-700">{selectedFile.name}</span>
                  {hasUnsavedChanges && (
                    <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">
                      未保存
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      handleSaveFile(currentEditorContent)
                    }}
                    disabled={!hasUnsavedChanges}
                    className="text-xs"
                  >
                    保存 (Ctrl+S)
                  </Button>
                </div>
              </div>

              {/* Markdown 编辑器 */}
              <div className="flex-1 overflow-hidden">
                <MarkdownEditor
                  initialValue={currentFileContent}
                  onChange={handleEditorChange}
                  readonly={false}
                  height="100%"
                  placeholder={`开始编写 ${selectedFile.name}...`}
                  currentPageName={selectedFile.name.replace('.md', '')}
                  wikiLinkConfig={wikiLinkConfig}
                  onPageClick={handleWikiLinkClick}
                  onLinkCreate={(source, target) => {
                    // 处理链接创建，可以在这里更新双向链接数据库
                    console.log('WikiLink created:', source, '->', target)
                    // TODO: 集成到现有的双向链接系统
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-hidden flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-600">Markdown 编辑器</h3>
                <p className="text-gray-500">
                  {selectedFile
                    ? selectedFile.type === 'folder'
                      ? '请选择一个 Markdown 文件进行编辑'
                      : '当前文件不是 Markdown 格式'
                    : '请在左侧文件树中选择一个 Markdown 文件'
                  }
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Links Panel - 在专注模式下隐藏 */}
        {!isFocusMode && (
          <div className="lg:col-span-1 space-y-4">
            {selectedFile && selectedFile.type === 'file' ? (
              <>
                {/* Toggle Button */}
                <div className="flex items-center gap-2">
                  <Button
                    variant={showBacklinks ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setShowBacklinks(!showBacklinks)}
                    className="flex-1"
                  >
                    {showBacklinks ? t('pages.resources.links') : t('pages.resources.graph')}
                  </Button>
                </div>

                {showBacklinks ? (
                  <>
                    {/* 链接统计 */}
                    <LinkStatisticsCard statistics={linkStatistics} />

                    {/* 双向反向链接 */}
                    <BidirectionalBacklinks
                      currentPath={selectedFile.path}
                      backlinks={bidirectionalBacklinks}
                      onLinkClick={() => {}}
                    />

                    {/* 双向出链 */}
                    <BidirectionalOutlinks
                      currentPath={selectedFile.path}
                      outlinks={bidirectionalOutlinks}
                      onLinkClick={() => {}}
                    />
                  </>
                ) : (
                  <div className="p-8 text-center border border-dashed border-gray-300 rounded-lg bg-gray-50">
                    <div className="text-gray-500">
                      <h3 className="font-semibold mb-2">🔗 链接图谱</h3>
                      <p className="text-sm mb-4">链接图谱功能已移除</p>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="h-32 border rounded-lg flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <div className="text-2xl mb-1">🔗</div>
                  <p className="text-xs">{t('pages.resources.selectFileToSeeLinks')}</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={deleteDialogOpen}
        item={itemToDelete}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        isDeleting={isDeleting}
      />

      {/* Create File Confirmation Dialog */}
      <CreateFileConfirmDialog />
    </div>
  )
}

export default ResourcesPage
