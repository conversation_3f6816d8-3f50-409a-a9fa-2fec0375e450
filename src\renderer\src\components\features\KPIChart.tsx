import { useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import type { ProjectKPI } from '../../../../shared/types'

interface KPIChartProps {
  kpis: ProjectKPI[]
  className?: string
}

interface ChartDataPoint {
  kpi: ProjectKPI
  progress: number
  status: 'achieved' | 'on-track' | 'at-risk' | 'behind' | 'no-target'
  color: string
}

export function KPIChart({ kpis, className }: KPIChartProps) {
  const chartData = useMemo(() => {
    return kpis.map((kpi): ChartDataPoint => {
      if (!kpi.target) {
        return {
          kpi,
          progress: 0,
          status: 'no-target',
          color: '#6b7280'
        }
      }

      const current = parseFloat(kpi.value)
      const target = parseFloat(kpi.target)
      
      if (isNaN(current) || isNaN(target) || target === 0) {
        return {
          kpi,
          progress: 0,
          status: 'no-target',
          color: '#6b7280'
        }
      }

      const progress = Math.min((current / target) * 100, 100)
      
      let status: ChartDataPoint['status']
      let color: string

      if (progress >= 100) {
        status = 'achieved'
        color = '#10b981' // green
      } else if (progress >= 75) {
        status = 'on-track'
        color = '#3b82f6' // blue
      } else if (progress >= 50) {
        status = 'at-risk'
        color = '#f59e0b' // yellow
      } else {
        status = 'behind'
        color = '#ef4444' // red
      }

      return {
        kpi,
        progress,
        status,
        color
      }
    }).sort((a, b) => b.progress - a.progress) // Sort by progress descending
  }, [kpis])

  const stats = useMemo(() => {
    const total = chartData.length
    const achieved = chartData.filter(d => d.status === 'achieved').length
    const onTrack = chartData.filter(d => d.status === 'on-track').length
    const atRisk = chartData.filter(d => d.status === 'at-risk').length
    const behind = chartData.filter(d => d.status === 'behind').length
    const noTarget = chartData.filter(d => d.status === 'no-target').length

    const avgProgress = total > 0 
      ? chartData.reduce((sum, d) => sum + d.progress, 0) / total 
      : 0

    return {
      total,
      achieved,
      onTrack,
      atRisk,
      behind,
      noTarget,
      avgProgress: Math.round(avgProgress)
    }
  }, [chartData])

  const getStatusLabel = (status: ChartDataPoint['status']) => {
    switch (status) {
      case 'achieved': return 'Achieved'
      case 'on-track': return 'On Track'
      case 'at-risk': return 'At Risk'
      case 'behind': return 'Behind'
      case 'no-target': return 'No Target'
      default: return 'Unknown'
    }
  }

  const getStatusColor = (status: ChartDataPoint['status']) => {
    switch (status) {
      case 'achieved': return 'bg-green-100 text-green-800 border-green-200'
      case 'on-track': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'at-risk': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'behind': return 'bg-red-100 text-red-800 border-red-200'
      case 'no-target': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (kpis.length === 0) {
    return null
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          KPI Performance Overview
          <Badge variant="outline" className="text-xs">
            {stats.total} KPIs
          </Badge>
        </CardTitle>
        <CardDescription>
          Visual overview of all key performance indicators
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Overall Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="text-2xl font-bold text-green-600">{stats.achieved}</div>
            <div className="text-xs text-green-700">Achieved</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-2xl font-bold text-blue-600">{stats.onTrack}</div>
            <div className="text-xs text-blue-700">On Track</div>
          </div>
          <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="text-2xl font-bold text-yellow-600">{stats.atRisk}</div>
            <div className="text-xs text-yellow-700">At Risk</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="text-2xl font-bold text-red-600">{stats.behind}</div>
            <div className="text-xs text-red-700">Behind</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-2xl font-bold text-gray-600">{stats.avgProgress}%</div>
            <div className="text-xs text-gray-700">Avg Progress</div>
          </div>
        </div>

        {/* Progress Bar Chart */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground">Individual KPI Progress</h4>
          <div className="space-y-3">
            {chartData.map((data, index) => (
              <div key={data.kpi.id} className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <span className="font-medium truncate">{data.kpi.name}</span>
                    <Badge 
                      variant="outline" 
                      className={cn('text-xs', getStatusColor(data.status))}
                    >
                      {getStatusLabel(data.status)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>
                      {data.kpi.value}{data.kpi.unit && ` ${data.kpi.unit}`}
                    </span>
                    {data.kpi.target && (
                      <>
                        <span>/</span>
                        <span>
                          {data.kpi.target}{data.kpi.unit && ` ${data.kpi.unit}`}
                        </span>
                      </>
                    )}
                    {data.status !== 'no-target' && (
                      <span className="font-medium">
                        ({Math.round(data.progress)}%)
                      </span>
                    )}
                  </div>
                </div>
                
                {/* Progress Bar with Animation */}
                <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden relative">
                  <div
                    className="h-full rounded-full transition-all duration-1000 ease-out relative"
                    style={{
                      width: `${Math.max(data.progress, 2)}%`,
                      backgroundColor: data.color,
                      opacity: data.status === 'no-target' ? 0.3 : 1
                    }}
                  >
                    {/* Shimmer effect for active progress bars */}
                    {data.status !== 'no-target' && data.progress > 0 && (
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse" />
                    )}
                  </div>

                  {/* Pulse effect for achieved KPIs */}
                  {data.status === 'achieved' && (
                    <div
                      className={cn(
                        'absolute inset-0 rounded-full animate-ping opacity-20',
                        data.status === 'achieved' ? 'bg-green-500' : 'bg-blue-500'
                      )}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Donut Chart for Status Distribution */}
        <div className="flex items-center justify-center">
          <div className="relative w-32 h-32">
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              {/* Background circle */}
              <circle
                cx="50"
                cy="50"
                r="40"
                fill="none"
                stroke="#f3f4f6"
                strokeWidth="8"
              />
              
              {/* Progress segments */}
              {(() => {
                let cumulativePercentage = 0
                const radius = 40
                const circumference = 2 * Math.PI * radius
                
                return [
                  { count: stats.achieved, color: '#10b981', label: 'Achieved' },
                  { count: stats.onTrack, color: '#3b82f6', label: 'On Track' },
                  { count: stats.atRisk, color: '#f59e0b', label: 'At Risk' },
                  { count: stats.behind, color: '#ef4444', label: 'Behind' }
                ].map((segment, index) => {
                  if (segment.count === 0) return null
                  
                  const percentage = (segment.count / stats.total) * 100
                  const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`
                  const strokeDashoffset = -((cumulativePercentage / 100) * circumference)
                  
                  cumulativePercentage += percentage
                  
                  return (
                    <circle
                      key={index}
                      cx="50"
                      cy="50"
                      r={radius}
                      fill="none"
                      stroke={segment.color}
                      strokeWidth="8"
                      strokeDasharray={strokeDasharray}
                      strokeDashoffset={strokeDashoffset}
                      className="transition-all duration-500"
                    />
                  )
                })
              })()}
            </svg>
            
            {/* Center text */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-lg font-bold">{stats.total}</div>
                <div className="text-xs text-muted-foreground">KPIs</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default KPIChart
