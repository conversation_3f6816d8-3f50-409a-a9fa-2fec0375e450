/**
 * 增强版领域指标创建对话框
 * 支持习惯追踪、领域标准等高级功能
 */

import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { Checkbox } from '../ui/checkbox'
import { Badge } from '../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { 
  Target, 
  Calendar, 
  Clock, 
  Star, 
  Tag,
  Activity,
  CheckSquare,
  TrendingUp
} from 'lucide-react'
import type { AreaMetric } from '../../../../shared/types'

interface EnhancedCreateAreaMetricDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (metric: Omit<AreaMetric, 'id' | 'updatedAt' | 'areaId'> & {
    trackingType?: string
    habitConfig?: any
    standardConfig?: any
    isActive?: boolean
    priority?: string
    category?: string
    description?: string
  }) => void
  initialData?: AreaMetric | null
  areaId: string
}

interface HabitConfig {
  targetFrequency: number
  weeklyTarget: number
  reminderTime: string
  streakGoal: number
}

interface StandardConfig {
  criteria: string[]
  passingScore: number
  evaluationPeriod: string
}

export function EnhancedCreateAreaMetricDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  areaId
}: EnhancedCreateAreaMetricDialogProps) {
  const [formData, setFormData] = useState({
    name: '',
    value: '',
    target: '',
    unit: '',
    frequency: '',
    trackingType: 'metric',
    isActive: true,
    priority: 'medium',
    category: '',
    description: ''
  })

  const [habitConfig, setHabitConfig] = useState<HabitConfig>({
    targetFrequency: 7,
    weeklyTarget: 5,
    reminderTime: '09:00',
    streakGoal: 30
  })

  const [standardConfig, setStandardConfig] = useState<StandardConfig>({
    criteria: [],
    passingScore: 80,
    evaluationPeriod: 'monthly'
  })

  const [newCriterion, setNewCriterion] = useState('')

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name,
        value: initialData.value,
        target: initialData.target || '',
        unit: initialData.unit || '',
        frequency: initialData.frequency || '',
        trackingType: (initialData as any).trackingType || 'metric',
        isActive: (initialData as any).isActive ?? true,
        priority: (initialData as any).priority || 'medium',
        category: (initialData as any).category || '',
        description: (initialData as any).description || ''
      })

      if ((initialData as any).habitConfig) {
        setHabitConfig((initialData as any).habitConfig)
      }

      if ((initialData as any).standardConfig) {
        setStandardConfig((initialData as any).standardConfig)
      }
    } else {
      // 重置表单
      setFormData({
        name: '',
        value: '',
        target: '',
        unit: '',
        frequency: '',
        trackingType: 'metric',
        isActive: true,
        priority: 'medium',
        category: '',
        description: ''
      })
      setHabitConfig({
        targetFrequency: 7,
        weeklyTarget: 5,
        reminderTime: '09:00',
        streakGoal: 30
      })
      setStandardConfig({
        criteria: [],
        passingScore: 80,
        evaluationPeriod: 'monthly'
      })
    }
  }, [initialData, isOpen])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const submitData: any = {
      ...formData,
      target: formData.target || undefined,
      unit: formData.unit || undefined,
      frequency: formData.frequency || undefined,
      category: formData.category || undefined,
      description: formData.description || undefined
    }

    // 根据类型添加配置
    if (formData.trackingType === 'habit') {
      submitData.habitConfig = habitConfig
    } else if (formData.trackingType === 'standard') {
      submitData.standardConfig = standardConfig
    }

    onSubmit(submitData)
    onClose()
  }

  const addCriterion = () => {
    if (newCriterion.trim()) {
      setStandardConfig(prev => ({
        ...prev,
        criteria: [...prev.criteria, newCriterion.trim()]
      }))
      setNewCriterion('')
    }
  }

  const removeCriterion = (index: number) => {
    setStandardConfig(prev => ({
      ...prev,
      criteria: prev.criteria.filter((_, i) => i !== index)
    }))
  }

  const trackingTypeOptions = [
    { value: 'metric', label: '📊 Regular Metric', description: 'Standard numerical tracking' },
    { value: 'habit', label: '🎯 Daily Habit', description: 'Daily check-in and streak tracking' },
    { value: 'standard', label: '✅ Area Standard', description: 'Criteria-based evaluation' }
  ]

  const categoryOptions = [
    { value: 'health', label: '🏃 Health', color: 'bg-green-100 text-green-700' },
    { value: 'learning', label: '📚 Learning', color: 'bg-blue-100 text-blue-700' },
    { value: 'work', label: '💼 Work', color: 'bg-purple-100 text-purple-700' },
    { value: 'personal', label: '🌟 Personal', color: 'bg-yellow-100 text-yellow-700' },
    { value: 'finance', label: '💰 Finance', color: 'bg-emerald-100 text-emerald-700' },
    { value: 'social', label: '👥 Social', color: 'bg-pink-100 text-pink-700' }
  ]

  const priorityOptions = [
    { value: 'high', label: 'High', color: 'bg-red-100 text-red-700' },
    { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-700' },
    { value: 'low', label: 'Low', color: 'bg-gray-100 text-gray-700' }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {initialData ? 'Edit Area Metric' : 'Create New Area Metric'}
          </DialogTitle>
          <DialogDescription>
            Set up tracking for your area metrics, habits, or standards
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="config">Configuration</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Daily Exercise"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="trackingType">Type *</Label>
                  <Select
                    value={formData.trackingType}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, trackingType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {trackingTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-xs text-muted-foreground">{option.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="value">Current Value *</Label>
                  <Input
                    id="value"
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                    placeholder="0"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="target">Target</Label>
                  <Input
                    id="target"
                    value={formData.target}
                    onChange={(e) => setFormData(prev => ({ ...prev, target: e.target.value }))}
                    placeholder="100"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="unit">Unit</Label>
                  <Input
                    id="unit"
                    value={formData.unit}
                    onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                    placeholder="kg, hours, %"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what this metric tracks..."
                  rows={3}
                />
              </div>
            </TabsContent>

            <TabsContent value="config" className="space-y-4">
              {formData.trackingType === 'habit' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Target className="h-5 w-5" />
                      Habit Configuration
                    </CardTitle>
                    <CardDescription>
                      Set up your daily habit tracking preferences
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Target Frequency (per week)</Label>
                        <Input
                          type="number"
                          value={habitConfig.targetFrequency}
                          onChange={(e) => setHabitConfig(prev => ({ 
                            ...prev, 
                            targetFrequency: parseInt(e.target.value) || 0 
                          }))}
                          min="1"
                          max="7"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Weekly Target</Label>
                        <Input
                          type="number"
                          value={habitConfig.weeklyTarget}
                          onChange={(e) => setHabitConfig(prev => ({ 
                            ...prev, 
                            weeklyTarget: parseInt(e.target.value) || 0 
                          }))}
                          min="1"
                          max="7"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Reminder Time</Label>
                        <Input
                          type="time"
                          value={habitConfig.reminderTime}
                          onChange={(e) => setHabitConfig(prev => ({ 
                            ...prev, 
                            reminderTime: e.target.value 
                          }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Streak Goal (days)</Label>
                        <Input
                          type="number"
                          value={habitConfig.streakGoal}
                          onChange={(e) => setHabitConfig(prev => ({ 
                            ...prev, 
                            streakGoal: parseInt(e.target.value) || 0 
                          }))}
                          min="1"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {formData.trackingType === 'standard' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <CheckSquare className="h-5 w-5" />
                      Standard Configuration
                    </CardTitle>
                    <CardDescription>
                      Define criteria for area standard evaluation
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Evaluation Criteria</Label>
                      <div className="flex gap-2">
                        <Input
                          value={newCriterion}
                          onChange={(e) => setNewCriterion(e.target.value)}
                          placeholder="Add a criterion..."
                          onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCriterion())}
                        />
                        <Button type="button" onClick={addCriterion} variant="outline">
                          Add
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {standardConfig.criteria.map((criterion, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="cursor-pointer"
                            onClick={() => removeCriterion(index)}
                          >
                            {criterion} ×
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Passing Score (%)</Label>
                        <Input
                          type="number"
                          value={standardConfig.passingScore}
                          onChange={(e) => setStandardConfig(prev => ({ 
                            ...prev, 
                            passingScore: parseInt(e.target.value) || 0 
                          }))}
                          min="0"
                          max="100"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Evaluation Period</Label>
                        <Select
                          value={standardConfig.evaluationPeriod}
                          onValueChange={(value) => setStandardConfig(prev => ({ 
                            ...prev, 
                            evaluationPeriod: value 
                          }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                            <SelectItem value="quarterly">Quarterly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Category</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Priority</Label>
                  <Select
                    value={formData.priority}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorityOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <Select
                  value={formData.frequency}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, frequency: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: !!checked }))}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              {initialData ? 'Update' : 'Create'} Metric
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
