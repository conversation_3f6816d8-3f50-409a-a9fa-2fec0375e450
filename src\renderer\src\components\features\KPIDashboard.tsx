import { useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { cn } from '../../lib/utils'
import type { ProjectKPI } from '../../../../shared/types'

interface KPIDashboardProps {
  kpis: ProjectKPI[]
  className?: string
}

interface DashboardMetrics {
  totalKPIs: number
  achievedKPIs: number
  onTrackKPIs: number
  atRiskKPIs: number
  behindKPIs: number
  averageProgress: number
  topPerformer: ProjectKPI | null
  needsAttention: ProjectKPI | null
}

export function KPIDashboard({ kpis, className }: KPIDashboardProps) {
  const metrics = useMemo((): DashboardMetrics => {
    if (kpis.length === 0) {
      return {
        totalKPIs: 0,
        achievedKPIs: 0,
        onTrackKPIs: 0,
        atRiskKPIs: 0,
        behindKPIs: 0,
        averageProgress: 0,
        topPerformer: null,
        needsAttention: null
      }
    }

    const kpiData = kpis.map(kpi => {
      if (!kpi.target) {
        return { kpi, progress: 0, hasTarget: false }
      }

      const current = parseFloat(kpi.value)
      const target = parseFloat(kpi.target)
      
      if (isNaN(current) || isNaN(target) || target === 0) {
        return { kpi, progress: 0, hasTarget: false }
      }

      const progress = Math.min((current / target) * 100, 100)
      return { kpi, progress, hasTarget: true }
    })

    const kpisWithTargets = kpiData.filter(d => d.hasTarget)
    
    const achievedKPIs = kpisWithTargets.filter(d => d.progress >= 100).length
    const onTrackKPIs = kpisWithTargets.filter(d => d.progress >= 75 && d.progress < 100).length
    const atRiskKPIs = kpisWithTargets.filter(d => d.progress >= 50 && d.progress < 75).length
    const behindKPIs = kpisWithTargets.filter(d => d.progress < 50).length

    const averageProgress = kpisWithTargets.length > 0
      ? kpisWithTargets.reduce((sum, d) => sum + d.progress, 0) / kpisWithTargets.length
      : 0

    const topPerformer = kpisWithTargets.length > 0
      ? kpisWithTargets.reduce((max, current) => 
          current.progress > max.progress ? current : max
        ).kpi
      : null

    const needsAttention = kpisWithTargets.length > 0
      ? kpisWithTargets.reduce((min, current) => 
          current.progress < min.progress ? current : min
        ).kpi
      : null

    return {
      totalKPIs: kpis.length,
      achievedKPIs,
      onTrackKPIs,
      atRiskKPIs,
      behindKPIs,
      averageProgress: Math.round(averageProgress),
      topPerformer,
      needsAttention
    }
  }, [kpis])

  const getHealthScore = () => {
    if (metrics.totalKPIs === 0) return { score: 0, label: 'No Data', color: 'text-gray-500' }
    
    const score = (metrics.achievedKPIs * 100 + metrics.onTrackKPIs * 75 + metrics.atRiskKPIs * 50 + metrics.behindKPIs * 25) / metrics.totalKPIs
    
    if (score >= 90) return { score: Math.round(score), label: 'Excellent', color: 'text-green-600' }
    if (score >= 75) return { score: Math.round(score), label: 'Good', color: 'text-blue-600' }
    if (score >= 60) return { score: Math.round(score), label: 'Fair', color: 'text-yellow-600' }
    return { score: Math.round(score), label: 'Needs Improvement', color: 'text-red-600' }
  }

  const healthScore = getHealthScore()

  if (kpis.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>KPI Dashboard</CardTitle>
          <CardDescription>No KPIs to display</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">📊</div>
            <p className="text-sm">Add KPIs to see dashboard metrics</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          KPI Dashboard
          <Badge variant="outline" className="text-xs">
            {metrics.totalKPIs} KPIs
          </Badge>
        </CardTitle>
        <CardDescription>
          Real-time performance overview and key insights
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Health Score */}
        <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
          <div className="text-3xl font-bold mb-2 text-blue-900">
            {healthScore.score}
          </div>
          <div className={cn('text-lg font-medium mb-1', healthScore.color)}>
            {healthScore.label}
          </div>
          <div className="text-sm text-blue-700">
            Overall KPI Health Score
          </div>
        </div>

        {/* Status Distribution */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {metrics.achievedKPIs}
            </div>
            <div className="text-xs text-green-700 font-medium">Achieved</div>
            <div className="text-xs text-green-600">100% Complete</div>
          </div>
          
          <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {metrics.onTrackKPIs}
            </div>
            <div className="text-xs text-blue-700 font-medium">On Track</div>
            <div className="text-xs text-blue-600">75%+ Complete</div>
          </div>
          
          <div className="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="text-2xl font-bold text-yellow-600 mb-1">
              {metrics.atRiskKPIs}
            </div>
            <div className="text-xs text-yellow-700 font-medium">At Risk</div>
            <div className="text-xs text-yellow-600">50-74% Complete</div>
          </div>
          
          <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
            <div className="text-2xl font-bold text-red-600 mb-1">
              {metrics.behindKPIs}
            </div>
            <div className="text-xs text-red-700 font-medium">Behind</div>
            <div className="text-xs text-red-600">&lt;50% Complete</div>
          </div>
        </div>

        {/* Average Progress */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Average Progress</h4>
            <span className="text-sm font-bold">{metrics.averageProgress}%</span>
          </div>
          <Progress 
            value={metrics.averageProgress} 
            className="h-3"
          />
        </div>

        {/* Key Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Top Performer */}
          {metrics.topPerformer && (
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <div className="text-green-600">🏆</div>
                <h4 className="text-sm font-medium text-green-800">Top Performer</h4>
              </div>
              <div className="text-sm font-medium text-green-900">
                {metrics.topPerformer.name}
              </div>
              <div className="text-xs text-green-700">
                {metrics.topPerformer.value}
                {metrics.topPerformer.unit && ` ${metrics.topPerformer.unit}`}
                {metrics.topPerformer.target && (
                  <span> / {metrics.topPerformer.target}{metrics.topPerformer.unit && ` ${metrics.topPerformer.unit}`}</span>
                )}
              </div>
            </div>
          )}

          {/* Needs Attention */}
          {metrics.needsAttention && (
            <div className="p-4 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-center gap-2 mb-2">
                <div className="text-red-600">⚠️</div>
                <h4 className="text-sm font-medium text-red-800">Needs Attention</h4>
              </div>
              <div className="text-sm font-medium text-red-900">
                {metrics.needsAttention.name}
              </div>
              <div className="text-xs text-red-700">
                {metrics.needsAttention.value}
                {metrics.needsAttention.unit && ` ${metrics.needsAttention.unit}`}
                {metrics.needsAttention.target && (
                  <span> / {metrics.needsAttention.target}{metrics.needsAttention.unit && ` ${metrics.needsAttention.unit}`}</span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="p-4 bg-muted/50 rounded-lg">
          <h4 className="text-sm font-medium mb-3">💡 Quick Actions</h4>
          <div className="space-y-2 text-sm">
            {metrics.behindKPIs > 0 && (
              <div className="flex items-center gap-2 text-red-700">
                <span>•</span>
                <span>Review {metrics.behindKPIs} underperforming KPI{metrics.behindKPIs > 1 ? 's' : ''}</span>
              </div>
            )}
            {metrics.achievedKPIs > 0 && (
              <div className="flex items-center gap-2 text-green-700">
                <span>•</span>
                <span>Celebrate {metrics.achievedKPIs} achieved goal{metrics.achievedKPIs > 1 ? 's' : ''}!</span>
              </div>
            )}
            {metrics.onTrackKPIs > 0 && (
              <div className="flex items-center gap-2 text-blue-700">
                <span>•</span>
                <span>Maintain momentum on {metrics.onTrackKPIs} on-track KPI{metrics.onTrackKPIs > 1 ? 's' : ''}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default KPIDashboard
