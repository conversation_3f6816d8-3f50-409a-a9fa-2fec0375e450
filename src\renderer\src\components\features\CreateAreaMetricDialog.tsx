import { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { Badge } from '../ui/badge'
import { TrendingUp, Target } from 'lucide-react'
import type { AreaMetric } from '../../../../shared/types'

interface CreateAreaMetricDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (metric: Omit<AreaMetric, 'id' | 'updatedAt' | 'areaId'>) => void
  initialData?: AreaMetric | null
  areaId: string
}

const COMMON_UNITS = [
  { value: 'kg', label: 'Kilograms (kg)' },
  { value: 'lbs', label: 'Pounds (lbs)' },
  { value: 'cm', label: 'Centimeters (cm)' },
  { value: 'ft', label: 'Feet (ft)' },
  { value: '%', label: 'Percentage (%)' },
  { value: 'hours', label: 'Hours' },
  { value: 'minutes', label: 'Minutes' },
  { value: 'days', label: 'Days' },
  { value: 'count', label: 'Count' },
  { value: 'score', label: 'Score' },
  { value: 'rating', label: 'Rating' },
  { value: 'custom', label: 'Custom Unit' }
]

export function CreateAreaMetricDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  areaId
}: CreateAreaMetricDialogProps) {
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    value: initialData?.value || '0',
    target: initialData?.target || '',
    unit: initialData?.unit || '',
    frequency: initialData?.frequency || 'daily',
    // {{ AURA-X: Add - 添加方向选择. Approval: 寸止(ID:1738157400). }}
    direction: 'increase' as 'increase' | 'decrease'
  })
  const [customUnit, setCustomUnit] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name,
        value: initialData.value,
        target: initialData.target || '',
        unit: initialData.unit || '',
        frequency: initialData.frequency || 'daily',
        // {{ AURA-X: Add - 包含方向字段. Approval: 寸止(ID:1738157400). }}
        direction: (initialData as any).direction || 'increase'
      })
      
      // Check if unit is custom
      const isCommonUnit = COMMON_UNITS.some(u => u.value === initialData.unit)
      if (!isCommonUnit && initialData.unit) {
        setCustomUnit(initialData.unit)
        setFormData(prev => ({ ...prev, unit: 'custom' }))
      }
    }
  }, [initialData])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      return
    }

    setIsSubmitting(true)
    try {
      const metricData = {
        name: formData.name.trim(),
        value: formData.value.trim() || '0',
        target: formData.target.trim() || undefined,
        unit: formData.unit === 'custom' ? customUnit.trim() || undefined :
              formData.unit === 'none' ? undefined : formData.unit || undefined,
        frequency: formData.frequency,
        // {{ AURA-X: Add - 包含方向字段. Approval: 寸止(ID:1738157400). }}
        direction: formData.direction
      }

      await onSubmit(metricData)
      
      // Reset form
      setFormData({
        name: '',
        value: '0',
        target: '',
        unit: '',
        frequency: 'daily',
        // {{ AURA-X: Add - 重置方向字段. Approval: 寸止(ID:1738157400). }}
        direction: 'increase'
      })
      setCustomUnit('')
      onClose()
    } catch (error) {
      console.error('Failed to submit metric:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const getPreviewText = () => {
    const name = formData.name || 'Metric Name'
    const value = formData.value || '0'
    const unit = formData.unit === 'custom' ? customUnit : formData.unit
    const target = formData.target
    
    let preview = `${name}: ${value}`
    if (unit) preview += ` ${unit}`
    if (target) preview += ` / ${target}${unit ? ` ${unit}` : ''}`
    
    return preview
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {initialData ? 'Edit Metric' : 'Create New Metric'}
          </DialogTitle>
          <DialogDescription>
            {initialData 
              ? 'Update the metric details below'
              : 'Add a new key performance indicator to track your progress'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Metric Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Metric Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g., Weight, Body Fat %, Daily Steps"
              required
            />
          </div>

          {/* Current Value */}
          <div className="space-y-2">
            <Label htmlFor="value">Current Value</Label>
            <Input
              id="value"
              type="text"
              value={formData.value}
              onChange={(e) => setFormData({ ...formData, value: e.target.value })}
              placeholder="Enter current value"
            />
          </div>

          {/* Target Value */}
          <div className="space-y-2">
            <Label htmlFor="target">Target Value (Optional)</Label>
            <Input
              id="target"
              type="text"
              value={formData.target}
              onChange={(e) => setFormData({ ...formData, target: e.target.value })}
              placeholder="Enter target value"
            />
          </div>

          {/* Unit Selection */}
          <div className="space-y-2">
            <Label htmlFor="unit">Unit (Optional)</Label>
            <Select
              value={formData.unit}
              onValueChange={(value) => setFormData({ ...formData, unit: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No Unit</SelectItem>
                {COMMON_UNITS.map((unit) => (
                  <SelectItem key={unit.value} value={unit.value}>
                    {unit.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {formData.unit === 'custom' && (
              <Input
                value={customUnit}
                onChange={(e) => setCustomUnit(e.target.value)}
                placeholder="Enter custom unit"
                className="mt-2"
              />
            )}
          </div>

          {/* {{ AURA-X: Add - 方向选择器. Approval: 寸止(ID:1738157400). }} */}
          {/* Metric Direction */}
          <div className="space-y-2">
            <Label htmlFor="direction">Metric Direction</Label>
            <Select
              value={formData.direction}
              onValueChange={(value: 'increase' | 'decrease') => setFormData({ ...formData, direction: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="increase">
                  📈 Increase (Growth) - Current value should grow to target
                </SelectItem>
                <SelectItem value="decrease">
                  📉 Decrease (Reduction) - Current value should reduce to target
                </SelectItem>
              </SelectContent>
            </Select>
            <div className="text-xs text-muted-foreground">
              {formData.direction === 'decrease'
                ? 'Examples: Weight loss (90kg → 70kg), cost reduction, error reduction'
                : 'Examples: Revenue growth, skill improvement, completion rate'
              }
            </div>
          </div>

          {/* Recording Frequency */}
          <div className="space-y-2">
            <Label htmlFor="frequency">Recording Frequency</Label>
            <Select
              value={formData.frequency}
              onValueChange={(value) => setFormData({ ...formData, frequency: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select recording frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="as-needed">As Needed</SelectItem>
              </SelectContent>
            </Select>
            <div className="text-xs text-muted-foreground">
              How often do you plan to record data for this metric?
            </div>
          </div>

          {/* Preview */}
          <div className="space-y-2">
            <Label>Preview</Label>
            <div className="p-3 border rounded-lg bg-muted/50">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{getPreviewText()}</span>
                {formData.target && (
                  <Badge variant="outline" className="text-xs">
                    <Target className="h-3 w-3 mr-1" />
                    Target
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || !formData.name.trim()}>
              {isSubmitting 
                ? (initialData ? 'Updating...' : 'Creating...') 
                : (initialData ? 'Update Metric' : 'Create Metric')
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateAreaMetricDialog
