import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '../ui/tabs'
import KPIItem from './KPIItem'
import CreateKPIDialog from './CreateKPIDialog'
import KPI<PERSON>hart from './KPI<PERSON><PERSON>'
import KPITrends from './KPITrends'
import KPIDashboard from './KPIDashboard'
import KPIQuickInput from './KPIQuickInput'
import KPIHistory from './KPIHistory'
import BatchRecordDialog from './BatchRecordDialog'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import type { ProjectKPI, KPIRecord } from '../../../../shared/types'

interface KPIManagementProps {
  projectId: string
  className?: string
}

export function KPIManagement({ projectId, className }: KPIManagementProps) {
  const [kpis, setKpis] = useState<ProjectKPI[]>([])
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isBatchRecordDialogOpen, setIsBatchRecordDialogOpen] = useState(false)
  const [editingKPI, setEditingKPI] = useState<ProjectKPI | null>(null)
  const [loading, setLoading] = useState(true)
  const { addNotification } = useUIStore()

  // Load KPIs on component mount
  useEffect(() => {
    loadKPIs()
  }, [projectId])

  const loadKPIs = async () => {
    try {
      setLoading(true)
      const result = await databaseApi.getProjectKPIs(projectId)
      if (result.success) {
        setKpis(result.data || [])
      } else {
        console.error('Failed to load KPIs:', result.error)
        addNotification({
          type: 'error',
          title: 'Failed to load KPIs',
          message: result.error || 'Unknown error occurred'
        })
      }
    } catch (error) {
      console.error('Error loading KPIs:', error)
      addNotification({
        type: 'error',
        title: 'Error loading KPIs',
        message: 'An unexpected error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateKPI = async (kpiData: Omit<ProjectKPI, 'id' | 'updatedAt'>) => {
    try {
      const result = await databaseApi.createProjectKPI(kpiData)
      if (result.success) {
        setKpis(prev => [result.data, ...prev])
        addNotification({
          type: 'success',
          title: 'KPI Created',
          message: `KPI "${kpiData.name}" has been created successfully`
        })
      } else {
        throw new Error(result.error || 'Failed to create KPI')
      }
    } catch (error) {
      console.error('Failed to create KPI:', error)
      addNotification({
        type: 'error',
        title: 'Failed to create KPI',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
      throw error
    }
  }

  const handleUpdateKPI = async (kpi: ProjectKPI) => {
    try {
      const result = await databaseApi.updateProjectKPI({
        id: kpi.id,
        updates: {
          name: kpi.name,
          value: kpi.value,
          target: kpi.target,
          unit: kpi.unit,
          frequency: kpi.frequency
        }
      })
      
      if (result.success) {
        setKpis(prev => prev.map(k => k.id === kpi.id ? { ...k, ...result.data } : k))
        addNotification({
          type: 'success',
          title: 'KPI Updated',
          message: `KPI "${kpi.name}" has been updated successfully`
        })
      } else {
        throw new Error(result.error || 'Failed to update KPI')
      }
    } catch (error) {
      console.error('Failed to update KPI:', error)
      addNotification({
        type: 'error',
        title: 'Failed to update KPI',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleDeleteKPI = async (kpiId: string) => {
    const kpi = kpis.find(k => k.id === kpiId)
    if (!kpi) return

    if (!confirm(`Are you sure you want to delete the KPI "${kpi.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const result = await databaseApi.deleteProjectKPI(kpiId)
      if (result.success) {
        setKpis(prev => prev.filter(k => k.id !== kpiId))
        addNotification({
          type: 'success',
          title: 'KPI Deleted',
          message: `KPI "${kpi.name}" has been deleted successfully`
        })
      } else {
        throw new Error(result.error || 'Failed to delete KPI')
      }
    } catch (error) {
      console.error('Failed to delete KPI:', error)
      addNotification({
        type: 'error',
        title: 'Failed to delete KPI',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleEditKPI = (kpi: ProjectKPI) => {
    setEditingKPI(kpi)
    setIsCreateDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setIsCreateDialogOpen(false)
    setEditingKPI(null)
  }

  const handleRecordCreated = (record: KPIRecord) => {
    // 当创建新记录时，更新对应KPI的当前值
    setKpis(prev => prev.map(kpi =>
      kpi.id === record.kpiId
        ? { ...kpi, value: record.value, updatedAt: record.recordedAt }
        : kpi
    ))
  }

  const handleBatchRecordsCreated = (records: KPIRecord[]) => {
    // 批量更新KPI的当前值
    setKpis(prev => prev.map(kpi => {
      const record = records.find(r => r.kpiId === kpi.id)
      return record
        ? { ...kpi, value: record.value, updatedAt: record.recordedAt }
        : kpi
    }))
  }

  // Calculate overall KPI statistics
  const kpiStats = {
    total: kpis.length,
    achieved: kpis.filter(kpi => {
      if (!kpi.target) return false
      const current = parseFloat(kpi.value)
      const target = parseFloat(kpi.target)
      return !isNaN(current) && !isNaN(target) && current >= target
    }).length,
    onTrack: kpis.filter(kpi => {
      if (!kpi.target) return false
      const current = parseFloat(kpi.value)
      const target = parseFloat(kpi.target)
      if (isNaN(current) || isNaN(target)) return false
      const progress = (current / target) * 100
      return progress >= 75 && progress < 100
    }).length
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Key Performance Indicators</CardTitle>
          <CardDescription>Loading KPIs...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className={className}>
        <Tabs defaultValue="overview" className="w-full">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold flex items-center gap-2">
                Key Performance Indicators
                {kpis.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {kpis.length}
                  </Badge>
                )}
              </h3>
              <p className="text-sm text-muted-foreground">
                Track quantifiable metrics that measure project success
              </p>
            </div>
            <div className="flex gap-2">
              {kpis.length > 0 && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsBatchRecordDialogOpen(true)}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                  Batch Record
                </Button>
              )}
              <Button size="sm" onClick={() => setIsCreateDialogOpen(true)}>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add KPI
              </Button>
            </div>
          </div>

          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="records">Records</TabsTrigger>
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="charts">Charts</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>KPI Overview</CardTitle>
                <CardDescription>
                  Manage and monitor your key performance indicators
                </CardDescription>

                {/* KPI Statistics */}
                {kpis.length > 0 && (
                  <div className="flex items-center gap-4 mt-4 p-3 bg-muted/50 rounded-lg">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-green-600">{kpiStats.achieved}</div>
                      <div className="text-xs text-muted-foreground">Achieved</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-blue-600">{kpiStats.onTrack}</div>
                      <div className="text-xs text-muted-foreground">On Track</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-muted-foreground">{kpiStats.total}</div>
                      <div className="text-xs text-muted-foreground">Total KPIs</div>
                    </div>
                  </div>
                )}
              </CardHeader>

              <CardContent>
                {kpis.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <div className="text-4xl mb-2">📊</div>
                    <p className="text-sm">No KPIs defined yet</p>
                    <p className="text-xs mt-1">Add key performance indicators to track project success</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {kpis.map((kpi) => (
                      <KPIQuickInput
                        key={kpi.id}
                        kpi={kpi}
                        onRecordCreated={handleRecordCreated}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="records" className="space-y-4">
            {kpis.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8 text-muted-foreground">
                  <div className="text-4xl mb-2">📝</div>
                  <p className="text-sm">No KPIs to track</p>
                  <p className="text-xs mt-1">Create KPIs first to start recording data</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {kpis.map((kpi) => (
                  <KPIHistory key={kpi.id} kpi={kpi} />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="dashboard" className="space-y-4">
            <KPIDashboard kpis={kpis} />
          </TabsContent>

          <TabsContent value="charts" className="space-y-4">
            <KPIChart kpis={kpis} />
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <KPITrends kpis={kpis} />
          </TabsContent>
        </Tabs>
      </div>

      {/* Create/Edit KPI Dialog */}
      <CreateKPIDialog
        isOpen={isCreateDialogOpen}
        onClose={handleCloseDialog}
        onSubmit={editingKPI ? handleUpdateKPI : handleCreateKPI}
        initialData={editingKPI}
        projectId={projectId}
      />

      {/* Batch Record Dialog */}
      <BatchRecordDialog
        isOpen={isBatchRecordDialogOpen}
        onClose={() => setIsBatchRecordDialogOpen(false)}
        kpis={kpis}
        onRecordsCreated={handleBatchRecordsCreated}
      />
    </>
  )
}

export default KPIManagement
