# PaoLife 测试指南

## 概述

本指南将帮助您测试PaoLife应用的已完成功能。阶段一的基础架构搭建已全部完成，包含8个核心任务的所有功能。

## 环境要求

- Node.js 18.18.2+
- pnpm 8.0+
- Windows/macOS/Linux

## 快速开始

### 1. 安装依赖

```bash
cd PaoLife
pnpm install
```

### 2. 启动开发服务器

```bash
# 启动开发模式
pnpm run dev

# 或者构建并运行生产版本
pnpm run build
pnpm run start
```

### 3. 验证启动

应用启动后，您应该看到：

- Electron窗口正常打开
- 左侧显示导航栏
- 默认进入Dashboard页面
- 窗口右上角有窗口控制按钮

## 功能测试清单

### ✅ 任务1：项目依赖配置

**测试内容**：

- [ ] 应用正常启动，无依赖错误
- [ ] 开发工具正常工作（F12打开DevTools）
- [ ] 热重载功能正常（修改代码后自动刷新）

**验证方法**：

```bash
# 检查依赖安装
pnpm list

# 检查TypeScript编译
pnpm run typecheck

# 检查构建
pnpm run build
```

### ✅ 任务2：项目目录结构

**测试内容**：

- [ ] 目录结构清晰，符合Electron + React架构
- [ ] 主进程、渲染进程、预加载脚本分离
- [ ] 共享类型定义正确

**验证方法**：
检查以下目录结构：

```
src/
├── main/           # 主进程
├── preload/        # 预加载脚本
├── renderer/       # 渲染进程
└── shared/         # 共享类型
```

### ✅ 任务3：Prisma数据库连接

**测试内容**：

- [ ] 数据库文件正常创建（`prisma/dev.db`）
- [ ] 数据库表结构正确
- [ ] 数据库连接正常

**验证方法**：

```bash
# 查看数据库状态
pnpm prisma studio

# 检查数据库文件
ls -la prisma/dev.db

# 在应用中打开DevTools，检查控制台是否有数据库连接成功信息
```

### ✅ 任务4：Zustand状态管理

**测试内容**：

- [ ] 状态持久化正常工作
- [ ] 状态更新响应式
- [ ] DevTools集成正常

**验证方法**：

1. 打开应用DevTools
2. 查看Redux DevTools扩展（如果安装）
3. 在控制台执行：

```javascript
// 检查状态存储
console.log(window.__ZUSTAND_STORES__)
```

### ✅ 任务5：文件系统服务

**测试内容**：

- [ ] 文件监控服务正常启动
- [ ] 资源目录自动创建
- [ ] 文件操作API正常

**验证方法**：

1. 检查用户数据目录是否创建了`resources`文件夹
2. 在控制台查看文件系统初始化日志
3. 测试文件API（在DevTools控制台）：

```javascript
// 测试文件系统API
window.electronAPI.fileSystem.initialize().then(console.log)
```

### ✅ 任务6：IPC通信桥接

**测试内容**：

- [ ] 主进程与渲染进程通信正常
- [ ] API调用响应正确
- [ ] 事件转发正常

**验证方法**：
在DevTools控制台测试：

```javascript
// 测试数据库API
window.electronAPI.database.testConnection().then(console.log)

// 测试应用API
window.electronAPI.app.getVersion().then(console.log)

// 测试窗口API
window.electronAPI.window.toggleDevTools()
```

### ✅ 任务7：基础UI组件库

**测试内容**：

- [ ] shadcn/ui组件正常渲染
- [ ] 自定义组件功能正常
- [ ] 主题系统正确应用
- [ ] 组件展示页面可访问

**验证方法**：

1. 导航到 `/components` 页面
2. 检查所有组件是否正常显示
3. 测试交互功能：
   - 按钮点击
   - 对话框打开/关闭
   - 加载状态显示
   - 空状态展示

### ✅ 任务8：路由系统和主导航

**测试内容**：

- [ ] 所有页面路由正常工作
- [ ] 导航状态正确显示
- [ ] 面包屑导航正确
- [ ] 页面切换动画流畅

**验证方法**：

1. **导航测试**：
   - 点击左侧导航的每个菜单项
   - 验证页面正确切换
   - 检查活跃状态指示

2. **页面功能测试**：
   - Dashboard：查看统计数据和快速操作
   - Inbox：查看空状态提示
   - Projects：查看项目管理界面
   - Areas：查看领域管理界面
   - Resources：查看资源管理界面
   - Archive：查看归档管理界面
   - Reviews：查看回顾系统界面
   - Settings：查看设置选项

3. **面包屑测试**：
   - 检查每个页面的面包屑是否正确显示
   - 点击面包屑链接是否正确跳转

4. **窗口控制测试**：
   - 点击右上角的最小化按钮
   - 点击最大化按钮
   - 点击关闭按钮

## P.A.R.A.方法论视觉验证

### 颜色系统测试

验证P.A.R.A.四个类别的颜色是否正确应用：

1. **Projects（项目）**：绿色主题
   - 导航图标和文字
   - Dashboard中的项目卡片
   - 相关徽章和按钮

2. **Areas（领域）**：紫色主题
   - 导航图标和文字
   - Dashboard中的领域卡片
   - 相关徽章和按钮

3. **Resources（资源）**：橙色主题
   - 导航图标和文字
   - Dashboard中的资源卡片
   - 相关徽章和按钮

4. **Archive（归档）**：灰色主题
   - 导航图标和文字
   - 归档页面的视觉元素

### 主题切换测试

1. 进入Settings页面
2. 尝试切换主题（Light/Dark/System）
3. 验证颜色是否正确切换

## 性能测试

### 启动性能

- [ ] 应用启动时间 < 3秒
- [ ] 页面切换响应时间 < 500ms
- [ ] 内存使用合理（< 200MB）

### 功能性能

- [ ] 大量数据渲染流畅
- [ ] 状态更新响应及时
- [ ] 文件操作不阻塞UI

## 错误处理测试

### 错误边界测试

1. 在DevTools控制台故意触发错误：

```javascript
throw new Error('Test error boundary')
```

2. 验证错误边界是否正确捕获并显示错误页面

### 网络错误测试

1. 断开网络连接
2. 尝试执行需要网络的操作
3. 验证错误提示是否友好

## 常见问题排查

### 应用无法启动

1. 检查Node.js版本：`node --version`
2. 重新安装依赖：`rm -rf node_modules && pnpm install`
3. 清理构建缓存：`pnpm run clean`

### 数据库连接失败

1. 检查数据库文件权限
2. 重新生成数据库：`pnpm prisma db push`
3. 查看主进程日志

### 页面显示异常

1. 检查控制台错误信息
2. 验证CSS样式是否正确加载
3. 检查组件导入路径

### IPC通信失败

1. 检查preload脚本是否正确加载
2. 验证contextIsolation设置
3. 查看主进程和渲染进程日志

## 测试报告模板

完成测试后，请按以下格式记录结果：

```
## 测试报告

**测试时间**：2025-01-14
**测试环境**：Windows 11 / Node.js 18.18.2 / pnpm 8.15.1

### 功能测试结果
- [x] 任务1：项目依赖配置 - 通过
- [x] 任务2：项目目录结构 - 通过
- [x] 任务3：Prisma数据库连接 - 通过
- [x] 任务4：Zustand状态管理 - 通过
- [x] 任务5：文件系统服务 - 通过
- [x] 任务6：IPC通信桥接 - 通过
- [x] 任务7：基础UI组件库 - 通过
- [x] 任务8：路由系统和主导航 - 通过

### 性能测试结果
- 启动时间：2.1秒
- 内存使用：156MB
- 页面切换：平均320ms

### 发现的问题
- 无

### 总体评价
阶段一基础架构搭建完全成功，所有功能正常工作。
```

## 下一步

测试完成后，您可以：

1. 开始使用应用的基础功能
2. 为阶段二的功能开发做准备
3. 根据测试反馈优化现有功能
4. 开始实际的P.A.R.A.方法论实践

如有任何问题，请查看控制台日志或联系开发团队。
