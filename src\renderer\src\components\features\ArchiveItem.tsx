import { useState } from 'react'
import { Card, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import { cn } from '../../lib/utils'

export interface ArchiveItem {
  id: string
  title: string
  description?: string
  type: 'project' | 'area' | 'resource'
  originalStatus: string
  archivedAt: string
  archivedReason: 'completed' | 'cancelled' | 'inactive' | 'outdated' | 'manual'
  tags: string[]
  metadata: {
    originalId: string
    completionRate?: number
    lastActivity?: string
    size?: number
    attachments?: number
  }
}

interface ArchiveItemProps {
  item: ArchiveItem
  onRestore?: (item: ArchiveItem) => void
  onDelete?: (itemId: string) => void
  onView?: (item: ArchiveItem) => void
  className?: string
}

export function ArchiveItem({ item, onRestore, onDelete, onView, className }: ArchiveItemProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const { confirm, ConfirmDialog: ConfirmDialogComponent } = useConfirmDialog()

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'project':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
        )
      case 'area':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        )
      case 'resource':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        )
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
            />
          </svg>
        )
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'project':
        return 'bg-project/10 text-project border-project/20'
      case 'area':
        return 'bg-area/10 text-area border-area/20'
      case 'resource':
        return 'bg-resource/10 text-resource border-resource/20'
      default:
        return 'bg-archive/10 text-archive border-archive/20'
    }
  }

  const getReasonColor = (reason: string) => {
    switch (reason) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800'
      case 'outdated':
        return 'bg-orange-100 text-orange-800'
      case 'manual':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getReasonLabel = (reason: string) => {
    switch (reason) {
      case 'completed':
        return 'Completed'
      case 'cancelled':
        return 'Cancelled'
      case 'inactive':
        return 'Inactive'
      case 'outdated':
        return 'Outdated'
      case 'manual':
        return 'Manual'
      default:
        return 'Unknown'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`
    return `${Math.ceil(diffDays / 365)} years ago`
  }

  const handleRestore = () => {
    confirm({
      title: '恢复项目',
      description: `确定要将"${item.title}"恢复到活跃${item.type === 'project' ? '项目' : item.type === 'area' ? '领域' : '资源'}中吗？`,
      variant: 'default',
      confirmText: '恢复',
      cancelText: '取消',
      onConfirm: () => {
        onRestore?.(item)
      }
    })
  }

  const handleDelete = () => {
    confirm({
      title: '永久删除',
      description: `确定要永久删除"${item.title}"吗？此操作无法撤销。`,
      variant: 'destructive',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm: () => {
        onDelete?.(item.id)
      }
    })
  }

  return (
    <>
      <Card className={cn('group transition-all duration-200 hover:shadow-md', className)}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Type Icon */}
          <div className={cn('flex-shrink-0 p-2 rounded-lg border', getTypeColor(item.type))}>
            {getTypeIcon(item.type)}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-sm truncate">{item.title}</h3>
                {item.description && (
                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {item.description}
                  </p>
                )}
              </div>

              {/* Actions */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 5v.01M12 12v.01M12 19v.01"
                      />
                    </svg>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onView?.(item)}>View Details</DropdownMenuItem>
                  <DropdownMenuItem onClick={handleRestore}>Restore</DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleDelete}
                    className="text-red-600 focus:text-red-600"
                  >
                    Delete Permanently
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Metadata */}
            <div className="flex items-center gap-2 mb-2">
              <Badge
                variant="outline"
                className={cn('text-xs', getReasonColor(item.archivedReason))}
              >
                {getReasonLabel(item.archivedReason)}
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {item.type}
              </Badge>
              {item.metadata.completionRate !== undefined && (
                <Badge variant="outline" className="text-xs">
                  {item.metadata.completionRate}% complete
                </Badge>
              )}
            </div>

            {/* Tags */}
            {item.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-2">
                {item.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
                {item.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{item.tags.length - 3} more
                  </Badge>
                )}
              </div>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Archived {formatDate(item.archivedAt)}</span>

              <div className="flex items-center gap-3">
                {item.metadata.attachments && (
                  <span className="flex items-center gap-1">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                      />
                    </svg>
                    {item.metadata.attachments}
                  </span>
                )}

                {item.metadata.size && <span>{(item.metadata.size / 1024).toFixed(1)}KB</span>}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="h-4 w-4 p-0"
                >
                  <svg
                    className={cn('w-3 h-3 transition-transform', isExpanded && 'rotate-180')}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </Button>
              </div>
            </div>

            {/* Expanded Details */}
            {isExpanded && (
              <div className="mt-3 pt-3 border-t space-y-2 text-xs">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">Original Status:</span>
                    <div className="text-muted-foreground">{item.originalStatus}</div>
                  </div>
                  {item.metadata.lastActivity && (
                    <div>
                      <span className="font-medium">Last Activity:</span>
                      <div className="text-muted-foreground">
                        {formatDate(item.metadata.lastActivity)}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <span className="font-medium">Original ID:</span>
                  <div className="text-muted-foreground font-mono">{item.metadata.originalId}</div>
                </div>

                {item.tags.length > 3 && (
                  <div>
                    <span className="font-medium">All Tags:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {item.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          #{tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>

    {/* Confirm Dialog */}
    <ConfirmDialogComponent />
    </>
  )
}

export default ArchiveItem
