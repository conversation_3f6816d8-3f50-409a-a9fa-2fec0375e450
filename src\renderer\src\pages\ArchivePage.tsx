import { useState, useEffect } from 'react'
import { PageHeader, PageHeaderActions } from '../components/shared'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs'
import { useLanguage } from '../contexts/LanguageContext'
import ArchiveItem, {
  type ArchiveItem as ArchiveItemType
} from '../components/features/ArchiveItem'

export function ArchivePage() {
  const [items, setItems] = useState<ArchiveItemType[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterReason, setFilterReason] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('archivedAt')
  const [isLoading, setIsLoading] = useState(true)
  const { t } = useLanguage()

  // Mock data - in real implementation, this would come from a database
  useEffect(() => {
    const mockItems: ArchiveItemType[] = [
      {
        id: '1',
        title: 'Website Redesign Project',
        description: 'Complete overhaul of company website with new branding and improved UX',
        type: 'project',
        originalStatus: 'Completed',
        archivedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        archivedReason: 'completed',
        tags: ['web', 'design', 'branding'],
        metadata: {
          originalId: 'proj_001',
          completionRate: 100,
          lastActivity: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          attachments: 15
        }
      },
      {
        id: '2',
        title: 'Fitness & Health',
        description: 'Personal fitness goals and health tracking',
        type: 'area',
        originalStatus: 'Active',
        archivedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        archivedReason: 'inactive',
        tags: ['health', 'fitness', 'personal'],
        metadata: {
          originalId: 'area_002',
          lastActivity: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
          attachments: 8
        }
      },
      {
        id: '3',
        title: 'React 16 Documentation',
        description: 'Official React 16 documentation and learning materials',
        type: 'resource',
        originalStatus: 'Reference',
        archivedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        archivedReason: 'outdated',
        tags: ['react', 'documentation', 'learning'],
        metadata: {
          originalId: 'res_003',
          size: 2048000,
          lastActivity: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
          attachments: 3
        }
      },
      {
        id: '4',
        title: 'Mobile App Development',
        description: 'Cross-platform mobile app using React Native',
        type: 'project',
        originalStatus: 'In Progress',
        archivedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        archivedReason: 'cancelled',
        tags: ['mobile', 'react-native', 'app'],
        metadata: {
          originalId: 'proj_004',
          completionRate: 35,
          lastActivity: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
          attachments: 22
        }
      },
      {
        id: '5',
        title: 'Learning Spanish',
        description: 'Language learning resources and practice materials',
        type: 'area',
        originalStatus: 'Active',
        archivedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        archivedReason: 'manual',
        tags: ['language', 'learning', 'spanish'],
        metadata: {
          originalId: 'area_005',
          lastActivity: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          attachments: 12
        }
      }
    ]

    setTimeout(() => {
      setItems(mockItems)
      setIsLoading(false)
    }, 500)
  }, [])

  const handleRestore = (item: ArchiveItemType) => {
    console.log('Restore item:', item)
    // In real implementation, this would restore the item to its original location
    setItems((prev) => prev.filter((i) => i.id !== item.id))
  }

  const handleDelete = (itemId: string) => {
    console.log('Delete item:', itemId)
    // In real implementation, this would permanently delete the item
    setItems((prev) => prev.filter((item) => item.id !== itemId))
  }

  const handleView = (item: ArchiveItemType) => {
    console.log('View item:', item)
    // In real implementation, this would open a detailed view
  }

  // Filter and sort items
  const filteredItems = items
    .filter((item) => {
      const matchesSearch =
        !searchQuery ||
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesType = filterType === 'all' || item.type === filterType
      const matchesReason = filterReason === 'all' || item.archivedReason === filterReason

      return matchesSearch && matchesType && matchesReason
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title)
        case 'type':
          return a.type.localeCompare(b.type)
        case 'archivedAt':
        default:
          return new Date(b.archivedAt).getTime() - new Date(a.archivedAt).getTime()
      }
    })

  const getItemCounts = () => {
    const projects = items.filter((item) => item.type === 'project').length
    const areas = items.filter((item) => item.type === 'area').length
    const resources = items.filter((item) => item.type === 'resource').length
    return { projects, areas, resources, total: items.length }
  }

  const counts = getItemCounts()

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader
        title={t('pages.archive.title')}
        description={t('pages.archive.description')}
        badge={{
          text: `${counts.total} ${t('enums.types.project').toLowerCase()}`,
          variant: 'outline'
        }}
        actions={
          <PageHeaderActions.Settings onClick={() => console.log('Archive settings')}>
            {t('pages.archive.settings')}
          </PageHeaderActions.Settings>
        }
        className="border-l-4 border-archive pl-6"
      />

      {/* Archive Categories */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-project">P</Badge>
              {t('pages.archive.archivedProjects')}
            </CardTitle>
            <CardDescription>{t('pages.archive.completedOrCancelled')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.projects}</div>
            <p className="text-xs text-muted-foreground">
              {counts.projects > 0
                ? t('pages.archive.lastArchivedRecently')
                : t('pages.archive.noArchivedItems')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-area">A</Badge>
              {t('pages.archive.archivedAreas')}
            </CardTitle>
            <CardDescription>{t('pages.archive.noLongerMaintained')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.areas}</div>
            <p className="text-xs text-muted-foreground">
              {counts.areas > 0
                ? t('pages.archive.lastArchivedRecently')
                : t('pages.archive.noArchivedItems')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-resource">R</Badge>
              {t('pages.archive.archivedResources')}
            </CardTitle>
            <CardDescription>{t('pages.archive.noLongerRelevant')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.resources}</div>
            <p className="text-xs text-muted-foreground">
              {counts.resources > 0
                ? t('pages.archive.lastArchivedRecently')
                : t('pages.archive.noArchivedItems')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder={t('pages.archive.searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <div className="flex gap-2">
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('pages.inbox.filters.type.allTypes')}</SelectItem>
              <SelectItem value="project">📋 {t('enums.types.project')}</SelectItem>
              <SelectItem value="area">🏠 {t('enums.types.area')}</SelectItem>
              <SelectItem value="resource">📄 {t('enums.types.resource')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterReason} onValueChange={setFilterReason}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Reason" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Reasons</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="outdated">Outdated</SelectItem>
              <SelectItem value="manual">Manual</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Sort" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="archivedAt">Date Archived</SelectItem>
              <SelectItem value="title">Title</SelectItem>
              <SelectItem value="type">Type</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Archive Items */}
      <Tabs defaultValue="items" className="space-y-4">
        <TabsList>
          <TabsTrigger value="items">Archived Items</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="items" className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center gap-2 text-muted-foreground">
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                <span>Loading archived items...</span>
              </div>
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="text-center py-12">
              {items.length === 0 ? (
                <div className="text-muted-foreground">
                  <div className="text-4xl mb-2">📦</div>
                  <p className="text-sm">No archived items yet</p>
                  <p className="text-xs mt-1">Items will appear here when archived</p>
                </div>
              ) : (
                <div className="text-muted-foreground">
                  <div className="text-4xl mb-2">🔍</div>
                  <p className="text-sm">No items match your filters</p>
                  <p className="text-xs mt-1">Try adjusting your search or filters</p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredItems.map((item) => (
                <ArchiveItem
                  key={item.id}
                  item={item}
                  onRestore={handleRestore}
                  onDelete={handleDelete}
                  onView={handleView}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Archive Management</CardTitle>
              <CardDescription>Manage your archived items and cleanup policies</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Auto-archive completed projects</h4>
                  <p className="text-sm text-muted-foreground">
                    Automatically move completed projects to archive after 30 days
                  </p>
                </div>
                <Badge variant="secondary">Enabled</Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Cleanup old archives</h4>
                  <p className="text-sm text-muted-foreground">
                    Permanently delete archived items older than 1 year
                  </p>
                </div>
                <Badge variant="outline">Disabled</Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Archive notifications</h4>
                  <p className="text-sm text-muted-foreground">
                    Get notified when items are automatically archived
                  </p>
                </div>
                <Badge variant="secondary">Enabled</Badge>
              </div>

              <div className="pt-4 border-t">
                <Button variant="outline" className="w-full">
                  Export Archive Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ArchivePage
