/**
 * 领域指标适配器 - 将AreaMetric数据转换为ProjectKPI格式
 * 用于复用项目模块的KPI组件（Dashboard、Chart、Trends）
 */

import type { AreaMetric, ProjectKPI } from '../../../shared/types'
// {{ AURA-X: Add - 导入通用KPI计算器. Approval: 寸止(ID:**********). }}
import {
  getKPIDirection,
  calculateKPIProgress,
  calculateKPIStatistics,
  generateKPIRecommendations
} from './kpiProgressCalculator'

/**
 * 将AreaMetric转换为ProjectKPI格式
 * 这样可以直接复用项目模块的KPI组件
 */
export function adaptAreaMetricToProjectKPI(areaMetric: AreaMetric): ProjectKPI {
  return {
    id: areaMetric.id,
    name: areaMetric.name,
    value: areaMetric.value,
    target: areaMetric.target,
    unit: areaMetric.unit,
    frequency: areaMetric.frequency,
    updatedAt: areaMetric.updatedAt,
    // 使用areaId作为projectId，组件内部不会使用这个字段
    projectId: areaMetric.areaId
  }
}

/**
 * 批量转换AreaMetric数组为ProjectKPI数组
 */
export function adaptAreaMetricsToProjectKPIs(areaMetrics: AreaMetric[]): ProjectKPI[] {
  return areaMetrics.map(adaptAreaMetricToProjectKPI)
}

/**
 * 判断指标类型：增长型还是减少型
 * 使用通用KPI计算器
 */
export function getMetricDirection(areaMetric: AreaMetric): 'increase' | 'decrease' {
  // {{ AURA-X: Modify - 使用通用KPI计算器. Approval: 寸止(ID:**********). }}
  return getKPIDirection(areaMetric as any)
}

/**
 * 计算指标进度，支持增长型和减少型指标
 */
export function calculateMetricProgress(areaMetric: AreaMetric): number {
  // {{ AURA-X: Modify - 使用通用KPI计算器. Approval: 寸止(ID:**********). }}
  return calculateKPIProgress(areaMetric as any)
}

/**
 * 计算领域指标的健康评分
 * 基于目标达成情况和指标状态，支持双向KPI
 */
export function calculateAreaHealthScore(areaMetrics: AreaMetric[]): {
  score: number
  label: string
  color: string
  breakdown: {
    achieved: number
    onTrack: number
    atRisk: number
    behind: number
  }
} {
  if (areaMetrics.length === 0) {
    return {
      score: 0,
      label: 'No Data',
      color: 'text-gray-500',
      breakdown: { achieved: 0, onTrack: 0, atRisk: 0, behind: 0 }
    }
  }

  const metricsWithTargets = areaMetrics.filter(metric => metric.target)
  
  if (metricsWithTargets.length === 0) {
    return {
      score: 0,
      label: 'No Targets Set',
      color: 'text-gray-500',
      breakdown: { achieved: 0, onTrack: 0, atRisk: 0, behind: 0 }
    }
  }

  let achieved = 0
  let onTrack = 0
  let atRisk = 0
  let behind = 0

  metricsWithTargets.forEach(metric => {
    const progress = calculateMetricProgress(metric)

    if (progress >= 100) achieved++
    else if (progress >= 75) onTrack++
    else if (progress >= 50) atRisk++
    else behind++
  })

  const totalScore = (achieved * 100 + onTrack * 75 + atRisk * 50 + behind * 25) / metricsWithTargets.length

  let label: string
  let color: string

  if (totalScore >= 90) {
    label = 'Excellent'
    color = 'text-green-600'
  } else if (totalScore >= 75) {
    label = 'Good'
    color = 'text-blue-600'
  } else if (totalScore >= 60) {
    label = 'Fair'
    color = 'text-yellow-600'
  } else {
    label = 'Needs Improvement'
    color = 'text-red-600'
  }

  return {
    score: Math.round(totalScore),
    label,
    color,
    breakdown: { achieved, onTrack, atRisk, behind }
  }
}

/**
 * 获取领域指标的状态分布
 */
export function getAreaMetricStatusDistribution(areaMetrics: AreaMetric[]) {
  const healthScore = calculateAreaHealthScore(areaMetrics)
  return {
    total: areaMetrics.length,
    withTargets: areaMetrics.filter(m => m.target).length,
    ...healthScore.breakdown
  }
}

/**
 * 生成领域指标的改进建议
 */
export function generateAreaMetricRecommendations(areaMetrics: AreaMetric[]): string[] {
  // {{ AURA-X: Modify - 使用通用KPI计算器. Approval: 寸止(ID:**********). }}
  return generateKPIRecommendations(areaMetrics as any[])
}

/**
 * 检查指标是否为习惯类型
 * 基于指标名称和频率判断，兼容新旧数据结构
 */
export function isHabitMetric(areaMetric: AreaMetric): boolean {
  // {{ AURA-X: Add - 向后兼容性检查. Approval: 寸止(ID:**********). }}
  // 首先检查是否有新的trackingType字段
  const extendedMetric = areaMetric as any
  if (extendedMetric.trackingType) {
    return extendedMetric.trackingType === 'habit'
  }

  // 回退到基于名称和频率的判断（向后兼容）
  const habitKeywords = ['daily', '每日', 'habit', '习惯', 'routine', '例行']
  const habitFrequencies = ['daily', 'weekly', '每日', '每周']

  const nameContainsHabitKeyword = habitKeywords.some(keyword =>
    areaMetric.name.toLowerCase().includes(keyword.toLowerCase())
  )

  const hasHabitFrequency = habitFrequencies.some(freq =>
    areaMetric.frequency?.toLowerCase().includes(freq.toLowerCase())
  )

  return nameContainsHabitKeyword || hasHabitFrequency
}

/**
 * 获取习惯类型的指标
 */
export function getHabitMetrics(areaMetrics: AreaMetric[]): AreaMetric[] {
  return areaMetrics.filter(isHabitMetric)
}

/**
 * 获取非习惯类型的指标（常规指标）
 */
export function getRegularMetrics(areaMetrics: AreaMetric[]): AreaMetric[] {
  return areaMetrics.filter(metric => !isHabitMetric(metric))
}
