/**
 * 领域指标适配器 - 将AreaMetric数据转换为ProjectKPI格式
 * 用于复用项目模块的KPI组件（Dashboard、Chart、Trends）
 */

import type { AreaMetric, ProjectKPI } from '../../../shared/types'

/**
 * 将AreaMetric转换为ProjectKPI格式
 * 这样可以直接复用项目模块的KPI组件
 */
export function adaptAreaMetricToProjectKPI(areaMetric: AreaMetric): ProjectKPI {
  return {
    id: areaMetric.id,
    name: areaMetric.name,
    value: areaMetric.value,
    target: areaMetric.target,
    unit: areaMetric.unit,
    frequency: areaMetric.frequency,
    updatedAt: areaMetric.updatedAt,
    // 使用areaId作为projectId，组件内部不会使用这个字段
    projectId: areaMetric.areaId
  }
}

/**
 * 批量转换AreaMetric数组为ProjectKPI数组
 */
export function adaptAreaMetricsToProjectKPIs(areaMetrics: AreaMetric[]): ProjectKPI[] {
  return areaMetrics.map(adaptAreaMetricToProjectKPI)
}

/**
 * 计算领域指标的健康评分
 * 基于目标达成情况和指标状态
 */
export function calculateAreaHealthScore(areaMetrics: AreaMetric[]): {
  score: number
  label: string
  color: string
  breakdown: {
    achieved: number
    onTrack: number
    atRisk: number
    behind: number
  }
} {
  if (areaMetrics.length === 0) {
    return {
      score: 0,
      label: 'No Data',
      color: 'text-gray-500',
      breakdown: { achieved: 0, onTrack: 0, atRisk: 0, behind: 0 }
    }
  }

  const metricsWithTargets = areaMetrics.filter(metric => metric.target)
  
  if (metricsWithTargets.length === 0) {
    return {
      score: 0,
      label: 'No Targets Set',
      color: 'text-gray-500',
      breakdown: { achieved: 0, onTrack: 0, atRisk: 0, behind: 0 }
    }
  }

  let achieved = 0
  let onTrack = 0
  let atRisk = 0
  let behind = 0

  metricsWithTargets.forEach(metric => {
    const current = parseFloat(metric.value)
    const target = parseFloat(metric.target!)
    
    if (isNaN(current) || isNaN(target) || target === 0) return

    const progress = (current / target) * 100

    if (progress >= 100) achieved++
    else if (progress >= 75) onTrack++
    else if (progress >= 50) atRisk++
    else behind++
  })

  const totalScore = (achieved * 100 + onTrack * 75 + atRisk * 50 + behind * 25) / metricsWithTargets.length

  let label: string
  let color: string

  if (totalScore >= 90) {
    label = 'Excellent'
    color = 'text-green-600'
  } else if (totalScore >= 75) {
    label = 'Good'
    color = 'text-blue-600'
  } else if (totalScore >= 60) {
    label = 'Fair'
    color = 'text-yellow-600'
  } else {
    label = 'Needs Improvement'
    color = 'text-red-600'
  }

  return {
    score: Math.round(totalScore),
    label,
    color,
    breakdown: { achieved, onTrack, atRisk, behind }
  }
}

/**
 * 获取领域指标的状态分布
 */
export function getAreaMetricStatusDistribution(areaMetrics: AreaMetric[]) {
  const healthScore = calculateAreaHealthScore(areaMetrics)
  return {
    total: areaMetrics.length,
    withTargets: areaMetrics.filter(m => m.target).length,
    ...healthScore.breakdown
  }
}

/**
 * 生成领域指标的改进建议
 */
export function generateAreaMetricRecommendations(areaMetrics: AreaMetric[]): string[] {
  const recommendations: string[] = []
  const { breakdown } = calculateAreaHealthScore(areaMetrics)
  
  if (breakdown.behind > 0) {
    recommendations.push(`${breakdown.behind} metrics are significantly behind target. Consider reviewing your approach.`)
  }
  
  if (breakdown.atRisk > 0) {
    recommendations.push(`${breakdown.atRisk} metrics are at risk. Focus on these to prevent further decline.`)
  }
  
  if (breakdown.achieved > 0) {
    recommendations.push(`Great job! ${breakdown.achieved} metrics have achieved their targets.`)
  }
  
  const noTargets = areaMetrics.filter(m => !m.target).length
  if (noTargets > 0) {
    recommendations.push(`Set targets for ${noTargets} metrics to enable better tracking.`)
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Keep up the good work! All metrics are on track.')
  }
  
  return recommendations
}

/**
 * 检查指标是否为习惯类型
 * 基于指标名称和频率判断
 */
export function isHabitMetric(areaMetric: AreaMetric): boolean {
  const habitKeywords = ['daily', '每日', 'habit', '习惯', 'routine', '例行']
  const habitFrequencies = ['daily', 'weekly', '每日', '每周']
  
  const nameContainsHabitKeyword = habitKeywords.some(keyword => 
    areaMetric.name.toLowerCase().includes(keyword.toLowerCase())
  )
  
  const hasHabitFrequency = habitFrequencies.some(freq => 
    areaMetric.frequency?.toLowerCase().includes(freq.toLowerCase())
  )
  
  return nameContainsHabitKeyword || hasHabitFrequency
}

/**
 * 获取习惯类型的指标
 */
export function getHabitMetrics(areaMetrics: AreaMetric[]): AreaMetric[] {
  return areaMetrics.filter(isHabitMetric)
}

/**
 * 获取非习惯类型的指标（常规指标）
 */
export function getRegularMetrics(areaMetrics: AreaMetric[]): AreaMetric[] {
  return areaMetrics.filter(metric => !isHabitMetric(metric))
}
