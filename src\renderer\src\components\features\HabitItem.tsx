import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'

interface HabitRecord {
  date: string
  completed: boolean
  value?: number
  note?: string
}

interface Habit {
  id: string
  name: string
  description?: string
  frequency: 'daily' | 'weekly' | 'monthly'
  target?: number
  unit?: string
  color: string
  areaId?: string
  records: HabitRecord[]
  createdAt: string
  updatedAt: string
}

interface HabitItemProps {
  habit: Habit
  onEdit?: (habit: Habit) => void
  onDelete?: (habitId: string) => void
  onToggle?: (habitId: string, date: string, completed: boolean) => void
  className?: string
}

export function HabitItem({ habit, onEdit, onDelete, onToggle, className }: HabitItemProps) {
  const { t } = useLanguage()
  const [showDetails, setShowDetails] = useState(false)

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0]
  const todayRecord = habit.records.find((record) => record.date === today)
  const isCompletedToday = todayRecord?.completed || false

  // Calculate streak
  const calculateStreak = () => {
    let streak = 0
    const sortedRecords = habit.records
      .filter((record) => record.completed)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

    if (sortedRecords.length === 0) return 0

    const today = new Date()
    let currentDate = new Date(today)

    for (let i = 0; i < 365; i++) {
      // Check up to a year
      const dateStr = currentDate.toISOString().split('T')[0]
      const record = habit.records.find((r) => r.date === dateStr && r.completed)

      if (record) {
        streak++
      } else if (dateStr !== today.toISOString().split('T')[0]) {
        // If it's not today and no record, break the streak
        break
      }

      currentDate.setDate(currentDate.getDate() - 1)
    }

    return streak
  }

  // Calculate completion rate for the last 30 days
  const calculateCompletionRate = () => {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentRecords = habit.records.filter((record) => {
      const recordDate = new Date(record.date)
      return recordDate >= thirtyDaysAgo
    })

    if (recentRecords.length === 0) return 0
    const completedRecords = recentRecords.filter((record) => record.completed)
    return Math.round((completedRecords.length / 30) * 100)
  }

  // Get last 7 days for mini calendar
  const getLast7Days = () => {
    const days: Array<{
      date: string
      day: number
      completed: boolean
      isToday: boolean
    }> = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      const record = habit.records.find((r) => r.date === dateStr)
      days.push({
        date: dateStr,
        day: date.getDate(),
        completed: record?.completed || false,
        isToday: dateStr === today
      })
    }
    return days
  }

  const streak = calculateStreak()
  const completionRate = calculateCompletionRate()
  const last7Days = getLast7Days()

  const handleToggleToday = () => {
    onToggle?.(habit.id, today, !isCompletedToday)
  }

  const getFrequencyLabel = (frequency: string) => {
    switch (frequency) {
      case 'daily':
        return t('pages.habits.item.frequencyLabels.daily')
      case 'weekly':
        return t('pages.habits.item.frequencyLabels.weekly')
      case 'monthly':
        return t('pages.habits.item.frequencyLabels.monthly')
      default:
        return frequency
    }
  }

  return (
    <Card className={cn('group hover:shadow-md transition-all duration-200', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <div
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: habit.color }}
              />
              <CardTitle className="text-lg truncate">{habit.name}</CardTitle>
              <Badge variant="outline" className="text-xs">
                {getFrequencyLabel(habit.frequency)}
              </Badge>
            </div>
            {habit.description && (
              <p className="text-sm text-muted-foreground line-clamp-2">{habit.description}</p>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 5v.01M12 12v.01M12 19v.01"
                  />
                </svg>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit?.(habit)}>
                {t('pages.habits.item.editHabit')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowDetails(!showDetails)}>
                {showDetails
                  ? t('pages.habits.item.hideDetails')
                  : t('pages.habits.item.showDetails')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete?.(habit.id)}
                className="text-red-600 focus:text-red-600"
              >
                {t('pages.habits.item.deleteHabit')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Today's Action */}
        <div className="flex items-center justify-between p-3 rounded-lg bg-accent/50">
          <div className="flex items-center gap-3">
            <Button
              variant={isCompletedToday ? 'default' : 'outline'}
              size="sm"
              onClick={handleToggleToday}
              className={cn(
                'h-8 w-8 p-0 rounded-full',
                isCompletedToday && 'bg-green-600 hover:bg-green-700'
              )}
            >
              {isCompletedToday ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              ) : (
                <div className="w-2 h-2 rounded-full bg-current" />
              )}
            </Button>
            <div>
              <p className="text-sm font-medium">
                {isCompletedToday
                  ? t('pages.habits.item.completedToday')
                  : t('pages.habits.item.markAsDoneToday')}
              </p>
              {habit.target && habit.unit && (
                <p className="text-xs text-muted-foreground">
                  {t('pages.habits.item.target')}: {habit.target} {habit.unit}
                </p>
              )}
            </div>
          </div>

          <div className="text-right">
            <div className="text-sm font-medium">
              {streak} {t('pages.habits.item.dayStreak')}
            </div>
            <div className="text-xs text-muted-foreground">
              {completionRate}% {t('pages.habits.item.thisMonth')}
            </div>
          </div>
        </div>

        {/* Mini Calendar */}
        <div className="space-y-2">
          <div className="text-xs font-medium text-muted-foreground">
            {t('pages.habits.item.last7Days')}
          </div>
          <div className="flex gap-1">
            {last7Days.map((day) => (
              <div
                key={day.date}
                className={cn(
                  'flex-1 aspect-square rounded-md flex items-center justify-center text-xs font-medium transition-colors',
                  day.completed
                    ? 'bg-green-100 text-green-800 border border-green-200'
                    : 'bg-gray-100 text-gray-600',
                  day.isToday && 'ring-2 ring-primary ring-offset-1'
                )}
              >
                {day.day}
              </div>
            ))}
          </div>
        </div>

        {/* Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">{t('pages.habits.item.monthlyProgress')}</span>
            <span className="font-medium">{completionRate}%</span>
          </div>
          <Progress value={completionRate} className="h-2" />
        </div>

        {/* Details */}
        {showDetails && (
          <div className="space-y-3 pt-3 border-t">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div className="space-y-1">
                <div className="text-lg font-bold text-blue-600">{streak}</div>
                <div className="text-xs text-muted-foreground">
                  {t('pages.habits.item.currentStreak')}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-lg font-bold text-green-600">
                  {habit.records.filter((r) => r.completed).length}
                </div>
                <div className="text-xs text-muted-foreground">
                  {t('pages.habits.item.totalCompleted')}
                </div>
              </div>
            </div>

            <div className="text-xs text-muted-foreground">
              {t('pages.habits.item.created')} {new Date(habit.createdAt).toLocaleDateString()}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default HabitItem
