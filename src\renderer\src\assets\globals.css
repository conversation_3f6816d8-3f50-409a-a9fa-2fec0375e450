@tailwind base;
@tailwind components;
@tailwind utilities;

/* Milkdown 主题和样式 - 必须在 @tailwind base 之后导入 */
@import '@milkdown/theme-nord/style.css';
@import '@milkdown/crepe/theme/crepe.css';
@import '@milkdown/crepe/theme/nord.css';
@import '@milkdown/crepe/theme/common/prosemirror.css';
@import '@milkdown/crepe/theme/common/reset.css';
@import '@milkdown/crepe/theme/common/block-edit.css';
@import '@milkdown/crepe/theme/common/toolbar.css';
@import '@milkdown/crepe/theme/common/code-mirror.css';
@import '@milkdown/crepe/theme/common/image-block.css';
@import '@milkdown/crepe/theme/common/link-tooltip.css';
@import '@milkdown/crepe/theme/common/list-item.css';

/* Milkdown编辑器自定义样式 */
.markdown-editor-container .milkdown {
  height: 100%;
  border: none;
  outline: none;
}

.markdown-editor-container .milkdown .editor {
  height: 100%;
  padding: 1rem;
}

/* 确保编辑器内容区域正确显示 */
.markdown-editor-container .ProseMirror {
  height: 100%;
  outline: none;
  padding: 1rem;
  font-size: 14px;
  line-height: 1.6;
}

@layer base {
  :root {
    /* 基础色彩系统 - 明亮主题 */
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47% 11%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47% 11%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 221.2 83.2% 53.3%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* P.A.R.A. 方法论色彩系统 - 更现代化的色调 */
    --project: 142.1 70.2% 45.3%;
    --project-foreground: 0 0% 100%;
    --area: 262.1 83.3% 57.8%;
    --area-foreground: 0 0% 100%;
    --resource: 24.6 95% 53.1%;
    --resource-foreground: 0 0% 100%;
    --archive: 215.4 16.3% 46.9%;
    --archive-foreground: 0 0% 100%;

    /* 状态色彩 - 更鲜明的对比 */
    --success: 142.1 70.2% 45.3%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --info: 199.89 89.09% 48.04%;
    --info-foreground: 0 0% 100%;

    /* 优先级色彩 */
    --priority-high: 0 84.2% 60.2%;
    --priority-medium: 38 92% 50%;
    --priority-low: 142.1 70.2% 45.3%;

    /* 侧边栏 - 更深色调提供对比 */
    --sidebar: 222.2 47% 11%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
  }

  .dark {
    /* 基础色彩系统 - 暗色主题 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 91.2% 59.8%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;

    /* P.A.R.A. 方法论色彩系统 - 暗色主题 */
    --project: 142.1 70.6% 45.3%;
    --project-foreground: 0 0% 100%;
    --area: 262.1 83.3% 57.8%;
    --area-foreground: 0 0% 100%;
    --resource: 20.5 90.2% 48.2%;
    --resource-foreground: 0 0% 100%;
    --archive: 215.4 16.3% 46.9%;
    --archive-foreground: 0 0% 100%;

    /* 状态色彩 - 暗色主题 */
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --info: 199.89 89.09% 48.04%;
    --info-foreground: 0 0% 100%;

    /* 优先级色彩 - 暗色主题 */
    --priority-high: 0 62.8% 30.6%;
    --priority-medium: 38 92% 50%;
    --priority-low: 142.1 70.6% 45.3%;

    /* 侧边栏 - 暗色主题 */
    --sidebar: 224 71.4% 4.1%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-border: 215 27.9% 16.9%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

@layer components {
  /* PARA Method Component Styles */
  .para-project {
    background-color: hsl(var(--project) / 0.2);
    color: hsl(var(--project));
    border: 1px solid hsl(var(--project) / 0.3);
    font-weight: 600;
  }
  .para-area {
    background-color: hsl(var(--area) / 0.2);
    color: hsl(var(--area));
    border: 1px solid hsl(var(--area) / 0.3);
    font-weight: 600;
  }
  .para-resource {
    background-color: hsl(var(--resource) / 0.2);
    color: hsl(var(--resource));
    border: 1px solid hsl(var(--resource) / 0.3);
    font-weight: 600;
  }
  .para-archive {
    background-color: hsl(var(--archive) / 0.2);
    color: hsl(var(--archive));
    border: 1px solid hsl(var(--archive) / 0.3);
    font-weight: 600;
  }

  /* Priority Styles */
  .priority-high {
    background-color: hsl(var(--priority-high) / 0.1);
    color: hsl(var(--priority-high));
    border-color: hsl(var(--priority-high) / 0.2);
  }
  .priority-medium {
    background-color: hsl(var(--priority-medium) / 0.1);
    color: hsl(var(--priority-medium));
    border-color: hsl(var(--priority-medium) / 0.2);
  }
  .priority-low {
    background-color: hsl(var(--priority-low) / 0.1);
    color: hsl(var(--priority-low));
    border-color: hsl(var(--priority-low) / 0.2);
  }

  /* Status Styles */
  .status-success {
    background-color: hsl(var(--success) / 0.1);
    color: hsl(var(--success));
    border-color: hsl(var(--success) / 0.2);
  }
  .status-warning {
    background-color: hsl(var(--warning) / 0.1);
    color: hsl(var(--warning));
    border-color: hsl(var(--warning) / 0.2);
  }
  .status-info {
    background-color: hsl(var(--info) / 0.1);
    color: hsl(var(--info));
    border-color: hsl(var(--info) / 0.2);
  }

  /* Layout Utilities */
  .sidebar-layout {
    background-color: hsl(var(--sidebar));
    border-right: 1px solid hsl(var(--sidebar-border));
  }

  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.4s ease-out;
  }

  /* Page Transition Utilities */
  .page-transition {
    transition:
      opacity 0.2s ease-in-out,
      transform 0.2s ease-in-out;
  }

  .page-enter {
    opacity: 0;
    transform: translateY(8px);
  }

  .page-enter-active {
    opacity: 1;
    transform: translateY(0);
  }

  /* Focus Styles */
  .focus-ring:focus {
    outline: none;
    box-shadow:
      0 0 0 2px hsl(var(--ring)),
      0 0 0 4px hsl(var(--background));
  }

  /* 隐藏滚动条样式 - 符合用户要求 */
  .scrollbar-hidden {
    /* Firefox */
    scrollbar-width: none;
    /* IE and Edge */
    -ms-overflow-style: none;
  }

  .scrollbar-hidden::-webkit-scrollbar {
    /* Chrome, Safari, Opera */
    display: none;
  }

  /* 自定义滚动条样式 - 仅在需要时显示 */
  .scrollbar-custom {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }

  .scrollbar-custom::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-custom::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.6);
  }

  .scrollbar-custom::-webkit-scrollbar-corner {
    background: transparent;
  }
}

@layer utilities {
  /* Text Utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Interaction Utilities */
  .clickable {
    cursor: pointer;
    transition:
      color 0.2s,
      background-color 0.2s;
  }

  .clickable:hover {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }

  /* Layout Utilities */
  .center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .center-x {
    display: flex;
    justify-content: center;
  }

  .center-y {
    display: flex;
    align-items: center;
  }

  /* Quick Action Button Styles */
  .quick-action-btn {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 2px dashed hsl(var(--muted-foreground) / 0.25);
    transition:
      border-color 0.2s,
      background-color 0.2s;
    text-align: center;
  }

  .quick-action-btn:hover {
    background-color: hsl(var(--accent) / 0.05);
  }

  .quick-action-icon {
    width: 2rem;
    height: 2rem;
    margin: 0 auto 0.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .quick-action-icon-primary {
    background-color: hsl(var(--primary) / 0.1);
  }
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Drag and Drop Styles */
.drop-target {
  position: relative;
}

.drop-target::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 2px;
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

.drag-overlay {
  pointer-events: none;
  transform-origin: center;
  animation: dragFloat 0.3s ease-out;
}

@keyframes dragFloat {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(1.05) rotate(3deg);
    opacity: 0.9;
  }
}

/* Drag handle hover effect */
.group:hover .drag-handle {
  opacity: 1;
}

/* Drop zone indicator */
.drop-zone-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: #3b82f6;
  border-radius: 1px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.drop-zone-indicator.active {
  opacity: 1;
}

/* Hide scrollbar */
.scrollbar-hide {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* ===== Vditor Editor Styles ===== */

/* Vditor 容器基础样式 */
.vditor {
  @apply border border-border rounded-lg bg-background;
  font-family: inherit;
}

/* Vditor 工具栏样式 */
.vditor-toolbar {
  @apply border-b border-border bg-muted/50;
  border-radius: calc(var(--radius) - 2px) calc(var(--radius) - 2px) 0 0;
}

.vditor-toolbar .vditor-toolbar__item {
  @apply text-muted-foreground hover:text-foreground hover:bg-accent/50;
  border-radius: calc(var(--radius) - 4px);
  transition: all 0.2s ease;
}

.vditor-toolbar .vditor-toolbar__item--current {
  @apply bg-accent text-accent-foreground;
}

.vditor-toolbar .vditor-toolbar__divider {
  @apply bg-border;
}

/* Vditor 编辑区域样式 */
.vditor-content {
  @apply bg-background text-foreground;
}

.vditor-ir {
  @apply bg-background text-foreground;
  font-family: var(--font-mono), 'JetBrains Mono', 'Fira Code', monospace;
}

.vditor-wysiwyg {
  @apply bg-background text-foreground;
}

.vditor-sv {
  @apply bg-background text-foreground;
  font-family: var(--font-mono), 'JetBrains Mono', 'Fira Code', monospace;
}

/* Vditor 预览区域样式 */
.vditor-preview {
  @apply bg-background text-foreground;
}

.vditor-preview .vditor-reset {
  @apply text-foreground;
}

/* Vditor 分割线样式 */
.vditor-resize {
  @apply bg-border hover:bg-accent;
}

/* Vditor 状态栏样式 */
.vditor-counter {
  @apply text-muted-foreground bg-muted/30;
  border-top: 1px solid hsl(var(--border));
}

/* Vditor 提示框样式 */
.vditor-hint {
  @apply bg-popover border border-border shadow-md;
  border-radius: var(--radius);
}

.vditor-hint .vditor-hint__item {
  @apply text-popover-foreground hover:bg-accent hover:text-accent-foreground;
}

.vditor-hint .vditor-hint__item--current {
  @apply bg-accent text-accent-foreground;
}

/* Vditor 深色主题特殊适配 */
.dark .vditor {
  border-color: hsl(var(--border));
  background-color: hsl(var(--background));
}

.dark .vditor-toolbar {
  background-color: hsl(var(--muted) / 0.5);
  border-bottom-color: hsl(var(--border));
}

.dark .vditor-content,
.dark .vditor-ir,
.dark .vditor-wysiwyg,
.dark .vditor-sv {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

.dark .vditor-preview {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Vditor 内容区域的 Markdown 样式 */
.vditor-reset h1,
.vditor-reset h2,
.vditor-reset h3,
.vditor-reset h4,
.vditor-reset h5,
.vditor-reset h6 {
  @apply text-foreground font-semibold;
}

.vditor-reset h1 {
  @apply text-3xl mt-8 mb-4;
}
.vditor-reset h2 {
  @apply text-2xl mt-6 mb-3;
}
.vditor-reset h3 {
  @apply text-xl mt-4 mb-2;
}
.vditor-reset h4 {
  @apply text-lg mt-3 mb-2;
}
.vditor-reset h5 {
  @apply text-base mt-2 mb-1;
}
.vditor-reset h6 {
  @apply text-sm mt-2 mb-1;
}

.vditor-reset p {
  @apply mb-4 leading-relaxed text-foreground;
}

.vditor-reset blockquote {
  @apply border-l-4 border-primary/30 pl-4 py-2 my-4 bg-muted/50 text-muted-foreground italic;
}

.vditor-reset code {
  @apply bg-muted px-1.5 py-0.5 rounded text-sm font-mono text-foreground;
}

.vditor-reset pre {
  @apply bg-muted p-4 rounded-lg overflow-x-auto my-4;
}

.vditor-reset pre code {
  @apply bg-transparent p-0;
}

.vditor-reset a {
  @apply text-primary hover:text-primary/80 hover:underline;
}

.vditor-reset ul,
.vditor-reset ol {
  @apply space-y-1 my-4;
}

.vditor-reset li {
  @apply py-1;
}

.vditor-reset table {
  @apply min-w-full border border-border rounded-lg my-4;
}

.vditor-reset th {
  @apply px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider bg-muted/50;
}

.vditor-reset td {
  @apply px-4 py-2 text-sm text-foreground border-t border-border;
}

.vditor-reset tr:hover {
  @apply bg-muted/30;
}
