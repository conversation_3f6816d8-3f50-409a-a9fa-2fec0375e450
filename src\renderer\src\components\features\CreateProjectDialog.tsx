import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { useAreaStore } from '../../store/areaStore'
import type { Project } from '../../../../shared/types'

interface CreateProjectDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void
  initialData?: Partial<Project>
}

export function CreateProjectDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData
}: CreateProjectDialogProps) {
  const { areas } = useAreaStore()
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    description: initialData?.description || '',
    goal: initialData?.goal || '',
    deliverable: initialData?.deliverable || '',
    status: initialData?.status || 'Not Started',
    progress: initialData?.progress || 0,
    startDate: initialData?.startDate
      ? new Date(initialData.startDate).toISOString().split('T')[0]
      : '',
    deadline: initialData?.deadline
      ? new Date(initialData.deadline).toISOString().split('T')[0]
      : '',
    areaId: initialData?.areaId || 'none',
    archived: initialData?.archived || false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const statusOptions = [
    { value: 'Not Started', label: 'Not Started', color: 'bg-gray-100 text-gray-800' },
    { value: 'In Progress', label: 'In Progress', color: 'bg-blue-100 text-blue-800' },
    { value: 'At Risk', label: 'At Risk', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'Paused', label: 'Paused', color: 'bg-gray-100 text-gray-800' },
    { value: 'Completed', label: 'Completed', color: 'bg-green-100 text-green-800' }
  ]

  const deliverableTypes = [
    { value: 'document', label: '📄 Document/Report', example: 'e.g., [[Project Report.md]]' },
    { value: 'metric', label: '📊 Quantifiable Metric', example: 'e.g., User satisfaction ≥ 95%' },
    { value: 'checklist', label: '✅ Checklist', example: 'e.g., Multiple deliverables' },
    { value: 'other', label: '🎯 Other', example: 'e.g., Custom deliverable' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) return

    setIsSubmitting(true)
    try {
      const projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'> = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        goal: formData.goal.trim() || null,
        deliverable: formData.deliverable.trim() || null,
        status: formData.status,
        progress: formData.progress,
        startDate: formData.startDate ? new Date(formData.startDate) : null,
        deadline: formData.deadline ? new Date(formData.deadline) : null,
        areaId: formData.areaId === 'none' ? null : formData.areaId,
        archived: formData.archived
      }

      await onSubmit(projectData)
      onClose()

      // Reset form
      setFormData({
        name: '',
        description: '',
        goal: '',
        deliverable: '',
        status: 'Not Started',
        progress: 0,
        startDate: '',
        deadline: '',
        areaId: 'none',
        archived: false
      })
    } catch (error) {
      console.error('Failed to create project:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-project/10 flex items-center justify-center">
              <span className="text-project font-semibold">P</span>
            </div>
            {initialData ? 'Edit Project' : 'Create New Project'}
          </DialogTitle>
          <DialogDescription>
            Projects are outcomes with specific deadlines and clear completion criteria.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Project Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                placeholder="Enter project name..."
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this project is about..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="goal">Project Goal</Label>
              <Textarea
                id="goal"
                value={formData.goal}
                onChange={(e) => setFormData((prev) => ({ ...prev, goal: e.target.value }))}
                placeholder="Why does this project exist? What's the ultimate purpose?"
                rows={2}
              />
            </div>
          </div>

          {/* Deliverable */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="deliverable">Final Deliverable</Label>
              <Textarea
                id="deliverable"
                value={formData.deliverable}
                onChange={(e) => setFormData((prev) => ({ ...prev, deliverable: e.target.value }))}
                placeholder="What specific outcome will mark this project as complete?"
                rows={2}
              />
              <div className="text-xs text-muted-foreground">
                <p className="mb-2">Examples:</p>
                <div className="space-y-1">
                  {deliverableTypes.map((type) => (
                    <div key={type.value} className="flex items-center gap-2">
                      <span>{type.label}</span>
                      <span className="text-muted-foreground/70">{type.example}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Status and Progress */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={cn('text-xs', option.color)}>
                          {option.label}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="progress">Progress (%)</Label>
              <Input
                id="progress"
                type="number"
                min="0"
                max="100"
                value={formData.progress}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, progress: parseInt(e.target.value) || 0 }))
                }
              />
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData((prev) => ({ ...prev, startDate: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="deadline">Deadline</Label>
              <Input
                id="deadline"
                type="date"
                value={formData.deadline}
                onChange={(e) => setFormData((prev) => ({ ...prev, deadline: e.target.value }))}
              />
            </div>
          </div>

          {/* Area Association */}
          <div className="space-y-2">
            <Label htmlFor="area">Associated Area (Optional)</Label>
            <Select
              value={formData.areaId}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, areaId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an area..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No area</SelectItem>
                {areas
                  .filter((area) => !area.archived)
                  .map((area) => (
                    <SelectItem key={area.id} value={area.id}>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-area"></div>
                        {area.name}
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={!formData.name.trim() || isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>{initialData ? 'Updating...' : 'Creating...'}</span>
                </div>
              ) : initialData ? (
                'Update Project'
              ) : (
                'Create Project'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateProjectDialog
