import { PageHeader } from '../components/shared'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { useLanguage, type Language } from '../contexts/LanguageContext'

export function SettingsPage() {
  const { language, setLanguage, t } = useLanguage()

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader title={t('settings.title')} description={t('settings.description')} />

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.general')}</CardTitle>
          <CardDescription>{t('settings.generalDescription')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username">{t('settings.displayName')}</Label>
              <Input id="username" placeholder={t('settings.displayName')} defaultValue="User" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="theme">{t('settings.theme')}</Label>
              <Select defaultValue="system">
                <SelectTrigger>
                  <SelectValue placeholder={t('settings.selectTheme')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">{t('settings.themes.light')}</SelectItem>
                  <SelectItem value="dark">{t('settings.themes.dark')}</SelectItem>
                  <SelectItem value="system">{t('settings.themes.system')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="language">{t('settings.language')}</Label>
            <Select value={language} onValueChange={handleLanguageChange}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder={t('settings.selectLanguage')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="zh">{t('settings.languages.zh')}</SelectItem>
                <SelectItem value="en">{t('settings.languages.en')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* P.A.R.A. Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            P.A.R.A. Method
            <Badge variant="secondary">Core</Badge>
          </CardTitle>
          <CardDescription>Configure your P.A.R.A. methodology preferences</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Auto-archive completed projects</h4>
                <p className="text-sm text-muted-foreground">
                  Automatically move completed projects to archive after specified days
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Input type="number" defaultValue="30" className="w-20" />
                <span className="text-sm text-muted-foreground">days</span>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Weekly review reminders</h4>
                <p className="text-sm text-muted-foreground">
                  Get notified to perform weekly reviews
                </p>
              </div>
              <Select defaultValue="sunday">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sunday">Sunday</SelectItem>
                  <SelectItem value="monday">Monday</SelectItem>
                  <SelectItem value="friday">Friday</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Default project template</h4>
                <p className="text-sm text-muted-foreground">
                  Template to use when creating new projects
                </p>
              </div>
              <Select defaultValue="basic">
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="detailed">Detailed</SelectItem>
                  <SelectItem value="agile">Agile</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data & Storage */}
      <Card>
        <CardHeader>
          <CardTitle>Data & Storage</CardTitle>
          <CardDescription>Manage your data, backups, and storage settings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">2.4 MB</div>
              <p className="text-sm text-muted-foreground">Database Size</p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">156</div>
              <p className="text-sm text-muted-foreground">Total Items</p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">12</div>
              <p className="text-sm text-muted-foreground">Backups</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" className="flex-1">
              Export Data
            </Button>
            <Button variant="outline" className="flex-1">
              Import Data
            </Button>
            <Button variant="outline" className="flex-1">
              Create Backup
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Advanced</CardTitle>
          <CardDescription>Advanced settings and developer options</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Enable debug mode</h4>
                <p className="text-sm text-muted-foreground">
                  Show additional debugging information
                </p>
              </div>
              <Badge variant="outline">Disabled</Badge>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Auto-update</h4>
                <p className="text-sm text-muted-foreground">
                  Automatically download and install updates
                </p>
              </div>
              <Badge variant="secondary">Enabled</Badge>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Analytics</h4>
                <p className="text-sm text-muted-foreground">
                  Help improve PaoLife by sharing anonymous usage data
                </p>
              </div>
              <Badge variant="secondary">Enabled</Badge>
            </div>
          </div>

          <div className="pt-4 border-t">
            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline">Reset Settings</Button>
              <Button variant="outline">Clear Cache</Button>
              <Button variant="destructive">Reset All Data</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* About */}
      <Card>
        <CardHeader>
          <CardTitle>About PaoLife</CardTitle>
          <CardDescription>Application information and credits</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              <strong>Version:</strong> 1.0.0
            </p>
            <p>
              <strong>Build:</strong> 2025.01.14
            </p>
            <p>
              <strong>Electron:</strong> 28.0.0
            </p>
            <p>
              <strong>Node.js:</strong> 18.18.2
            </p>
            <p>
              <strong>License:</strong> MIT
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SettingsPage
