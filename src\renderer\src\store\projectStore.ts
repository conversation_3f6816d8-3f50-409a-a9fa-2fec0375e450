import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { Project } from '../../../shared/types'
import { databaseApi } from '../lib/api'

export interface ProjectState {
  projects: Project[]
  currentProject: Project | null
  loading: boolean
  error: string | null
}

export interface ProjectActions {
  // Project CRUD operations
  setProjects: (projects: Project[]) => void
  addProject: (project: Project) => void
  updateProject: (id: string, updates: Partial<Project>) => void
  deleteProject: (id: string) => void
  archiveProject: (id: string) => void

  // Current project management
  setCurrentProject: (project: Project | null) => void

  // Loading and error states
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // Async operations
  fetchProjects: () => Promise<void>
  createProject: (data: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>

  // Optimistic updates with rollback
  optimisticUpdate: (id: string, updates: Partial<Project>, rollback?: () => void) => void
}

export type ProjectStore = ProjectState & ProjectActions

export const useProjectStore = create<ProjectStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        projects: [],
        currentProject: null,
        loading: false,
        error: null,

        // Actions
        setProjects: (projects) => set({ projects }),

        addProject: (project) =>
          set((state) => ({
            projects: [project, ...state.projects]
          })),

        updateProject: (id, updates) =>
          set((state) => ({
            projects: state.projects.map((project) =>
              project.id === id ? { ...project, ...updates } : project
            ),
            currentProject:
              state.currentProject?.id === id
                ? { ...state.currentProject, ...updates }
                : state.currentProject
          })),

        deleteProject: (id) =>
          set((state) => ({
            projects: state.projects.filter((project) => project.id !== id),
            currentProject: state.currentProject?.id === id ? null : state.currentProject
          })),

        archiveProject: (id) =>
          set((state) => ({
            projects: state.projects.map((project) =>
              project.id === id ? { ...project, archived: true } : project
            )
          })),

        setCurrentProject: (project) => set({ currentProject: project }),

        setLoading: (loading) => set({ loading }),

        setError: (error) => set({ error }),

        fetchProjects: async () => {
          set({ loading: true, error: null })
          try {
            const result = await databaseApi.getProjects()
            if (result.success) {
              // Convert database projects to frontend Project type
              const projects: Project[] =
                result.data?.map((dbProject: any) => ({
                  id: dbProject.id,
                  name: dbProject.name,
                  description: dbProject.description,
                  goal: dbProject.goal,
                  deliverable: null, // Not in database schema yet
                  status: 'Not Started', // Default status
                  progress: 0, // Default progress
                  startDate: null, // Not in database schema yet
                  deadline: null, // Not in database schema yet
                  areaId: null, // Not in database schema yet
                  archived: dbProject.archived || false,
                  createdAt: new Date(dbProject.createdAt),
                  updatedAt: new Date(dbProject.updatedAt)
                })) || []
              set({ projects, loading: false })
            } else {
              set({ error: result.error, loading: false })
            }
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch projects',
              loading: false
            })
          }
        },

        createProject: async (_data) => {
          set({ loading: true, error: null })
          try {
            // This will be implemented when IPC is ready
            // const result = await window.electron.ipcRenderer.invoke('create-project', data)
            // if (result.success) {
            //   get().addProject(result.data)
            // } else {
            //   set({ error: result.error })
            // }

            // Placeholder for now
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to create project',
              loading: false
            })
          }
        },

        optimisticUpdate: (id, updates, rollback) => {
          const originalProject = get().projects.find((p) => p.id === id)

          // Apply optimistic update
          get().updateProject(id, updates)

          // If rollback is provided, it means this is for async operation
          if (rollback && originalProject) {
            // Store rollback function for potential use
            // This would be called if the async operation fails
          }
        }
      }),
      {
        name: 'project-store',
        partialize: (state) => ({
          projects: state.projects,
          currentProject: state.currentProject
        })
      }
    ),
    {
      name: 'project-store'
    }
  )
)
