import { useState } from 'react'
import { Card, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Textarea } from '../ui/textarea'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'

export interface InboxItem {
  id: string
  content: string
  type: 'note' | 'task' | 'idea' | 'link' | 'file'
  priority: 'low' | 'medium' | 'high'
  tags: string[]
  processed: boolean
  processedAt?: string
  processedTo?: {
    type: 'project' | 'area' | 'resource' | 'archive'
    id: string
    name: string
  }
  createdAt: string
  updatedAt: string
}

interface InboxItemProps {
  item: InboxItem
  onEdit?: (item: InboxItem) => void
  onDelete?: (itemId: string) => void
  onProcess?: (
    itemId: string,
    destination: { type: 'project' | 'area' | 'resource' | 'archive'; id: string; name: string }
  ) => void
  onToggleProcessed?: (itemId: string) => void
  className?: string
}

export function InboxItem({
  item,
  onEdit,
  onDelete,
  onProcess,
  onToggleProcessed,
  className
}: InboxItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(item.content)
  const [showProcessOptions, setShowProcessOptions] = useState(false)

  const handleSaveEdit = () => {
    if (editContent.trim() && editContent !== item.content) {
      onEdit?.({
        ...item,
        content: editContent.trim(),
        updatedAt: new Date().toISOString()
      })
    }
    setIsEditing(false)
    setEditContent(item.content)
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditContent(item.content)
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'note':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
        )
      case 'task':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
            />
          </svg>
        )
      case 'idea':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
            />
          </svg>
        )
      case 'link':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
            />
          </svg>
        )
      case 'file':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        )
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v9a2 2 0 01-2 2h-1M9 7h1"
            />
          </svg>
        )
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'note':
        return 'bg-blue-100 text-blue-800'
      case 'task':
        return 'bg-green-100 text-green-800'
      case 'idea':
        return 'bg-purple-100 text-purple-800'
      case 'link':
        return 'bg-orange-100 text-orange-800'
      case 'file':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleProcess = (destination: {
    type: 'project' | 'area' | 'resource' | 'archive'
    id: string
    name: string
  }) => {
    onProcess?.(item.id, destination)
    setShowProcessOptions(false)
  }

  return (
    <Card
      className={cn(
        'group transition-all duration-200',
        item.processed ? 'opacity-60 bg-muted/20' : 'hover:shadow-md',
        className
      )}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Type Icon */}
          <div className={cn('flex-shrink-0 p-2 rounded-lg', getTypeColor(item.type))}>
            {getTypeIcon(item.type)}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            {isEditing ? (
              <div className="space-y-3">
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  placeholder="Edit content..."
                  rows={3}
                  className="resize-none"
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleSaveEdit}>
                    Save
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <p
                  className={cn(
                    'text-sm mb-2',
                    item.processed && 'line-through text-muted-foreground'
                  )}
                >
                  {item.content}
                </p>

                {/* Tags and Metadata */}
                <div className="flex items-center gap-2 mb-2">
                  <Badge
                    variant="outline"
                    className={cn('text-xs', getPriorityColor(item.priority))}
                  >
                    {item.priority}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {item.type}
                  </Badge>
                  {item.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>

                {/* Processed Info */}
                {item.processed && item.processedTo && (
                  <div className="text-xs text-muted-foreground mb-2">
                    Processed to {item.processedTo.type}: {item.processedTo.name}
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">
                    {new Date(item.createdAt).toLocaleDateString()}
                  </div>

                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    {!item.processed && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowProcessOptions(!showProcessOptions)}
                        className="h-7 px-2 text-xs"
                      >
                        Process
                      </Button>
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onToggleProcessed?.(item.id)}
                      className="h-7 px-2 text-xs"
                    >
                      {item.processed ? 'Unprocess' : 'Mark Done'}
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                          <svg
                            className="w-3 h-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 5v.01M12 12v.01M12 19v.01"
                            />
                          </svg>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setIsEditing(true)}>Edit</DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => onDelete?.(item.id)}
                          className="text-red-600 focus:text-red-600"
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Process Options */}
                {showProcessOptions && !item.processed && (
                  <div className="mt-3 p-3 border rounded-lg bg-accent/20">
                    <div className="text-xs font-medium mb-2">Process to:</div>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          handleProcess({ type: 'project', id: 'new', name: 'New Project' })
                        }
                        className="justify-start"
                      >
                        📋 Project
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleProcess({ type: 'area', id: 'new', name: 'New Area' })}
                        className="justify-start"
                      >
                        🏠 Area
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          handleProcess({ type: 'resource', id: 'new', name: 'New Resource' })
                        }
                        className="justify-start"
                      >
                        📄 Resource
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          handleProcess({ type: 'archive', id: 'archive', name: 'Archive' })
                        }
                        className="justify-start"
                      >
                        📦 Archive
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default InboxItem
