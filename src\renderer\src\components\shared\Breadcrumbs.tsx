import { Link, useMatches } from 'react-router-dom'
import { cn } from '../../lib/utils'

interface BreadcrumbMatch {
  id: string
  pathname: string
  params: Record<string, string>
  data: any
  handle?: {
    crumb?: (data: any) => string
  }
}

export function Breadcrumbs() {
  const matches = useMatches() as BreadcrumbMatch[]

  // Filter matches that have breadcrumb data
  const crumbs = matches
    .filter((match) => Boolean(match.handle?.crumb))
    .map((match) => ({
      label: match.handle?.crumb?.(match.data) || 'Unknown',
      pathname: match.pathname,
      isLast: false
    }))

  // Mark the last crumb
  if (crumbs.length > 0) {
    crumbs[crumbs.length - 1].isLast = true
  }

  if (crumbs.length === 0) {
    return null
  }

  return (
    <nav className="flex items-center space-x-2 text-sm" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {crumbs.map((crumb, index) => (
          <li key={crumb.pathname} className="flex items-center">
            {index > 0 && (
              <svg
                className="w-4 h-4 mx-2 text-muted-foreground"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            )}

            {crumb.isLast ? (
              <span className="font-medium text-foreground">{crumb.label}</span>
            ) : (
              <Link
                to={crumb.pathname}
                className={cn(
                  'text-muted-foreground hover:text-foreground transition-colors',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-1'
                )}
              >
                {crumb.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

export default Breadcrumbs
