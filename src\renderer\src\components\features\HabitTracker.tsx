/**
 * 习惯追踪组件 - 专门用于追踪日常习惯和重复性任务
 * 支持每日打卡、周频次统计、连续天数追踪等功能
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { Calendar, CheckCircle, Circle, Flame, Target, TrendingUp } from 'lucide-react'
import { cn } from '../../lib/utils'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import type { AreaMetric, AreaMetricRecord } from '../../../../shared/types'

interface HabitTrackerProps {
  metric: AreaMetric
  onRecordCreated?: (record: AreaMetricRecord) => void
  className?: string
}

interface HabitStats {
  currentStreak: number
  longestStreak: number
  completionRate: number
  thisWeekCount: number
  thisMonthCount: number
  totalCount: number
}

export function HabitTracker({ metric, onRecordCreated, className }: HabitTrackerProps) {
  const [records, setRecords] = useState<AreaMetricRecord[]>([])
  const [stats, setStats] = useState<HabitStats>({
    currentStreak: 0,
    longestStreak: 0,
    completionRate: 0,
    thisWeekCount: 0,
    thisMonthCount: 0,
    totalCount: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isCheckedToday, setIsCheckedToday] = useState(false)
  const { addNotification } = useUIStore()

  useEffect(() => {
    loadHabitData()
  }, [metric.id])

  const loadHabitData = async () => {
    try {
      const result = await databaseApi.getAreaMetricRecords(metric.id)
      if (result.success) {
        const habitRecords = result.data || []
        setRecords(habitRecords)
        calculateStats(habitRecords)
        checkTodayStatus(habitRecords)
      }
    } catch (error) {
      console.error('Failed to load habit data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const calculateStats = (habitRecords: AreaMetricRecord[]) => {
    const today = new Date()
    const startOfWeek = new Date(today)
    startOfWeek.setDate(today.getDate() - today.getDay())
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)

    // 按日期排序记录
    const sortedRecords = habitRecords
      .sort((a, b) => new Date(a.recordedAt).getTime() - new Date(b.recordedAt).getTime())

    // 计算本周和本月完成次数
    const thisWeekCount = habitRecords.filter(record => 
      new Date(record.recordedAt) >= startOfWeek
    ).length

    const thisMonthCount = habitRecords.filter(record => 
      new Date(record.recordedAt) >= startOfMonth
    ).length

    // 计算连续天数
    const { currentStreak, longestStreak } = calculateStreaks(sortedRecords)

    // 计算完成率（基于最近30天）
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(today.getDate() - 30)
    const recentRecords = habitRecords.filter(record => 
      new Date(record.recordedAt) >= thirtyDaysAgo
    )
    const completionRate = (recentRecords.length / 30) * 100

    setStats({
      currentStreak,
      longestStreak,
      completionRate: Math.min(completionRate, 100),
      thisWeekCount,
      thisMonthCount,
      totalCount: habitRecords.length
    })
  }

  const calculateStreaks = (sortedRecords: AreaMetricRecord[]) => {
    if (sortedRecords.length === 0) {
      return { currentStreak: 0, longestStreak: 0 }
    }

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // 获取每天的记录（去重）
    const dailyRecords = new Set(
      sortedRecords.map(record => {
        const date = new Date(record.recordedAt)
        date.setHours(0, 0, 0, 0)
        return date.getTime()
      })
    )

    const sortedDays = Array.from(dailyRecords).sort((a, b) => b - a)

    // 计算当前连续天数
    let currentStreak = 0
    let checkDate = new Date(today)

    for (let i = 0; i < sortedDays.length; i++) {
      if (sortedDays[i] === checkDate.getTime()) {
        currentStreak++
        checkDate.setDate(checkDate.getDate() - 1)
      } else if (sortedDays[i] < checkDate.getTime()) {
        break
      }
    }

    // 计算最长连续天数
    let longestStreak = 0
    let tempStreak = 1
    
    for (let i = 1; i < sortedDays.length; i++) {
      const prevDay = sortedDays[i - 1]
      const currentDay = sortedDays[i]
      const dayDiff = (prevDay - currentDay) / (1000 * 60 * 60 * 24)
      
      if (dayDiff === 1) {
        tempStreak++
      } else {
        longestStreak = Math.max(longestStreak, tempStreak)
        tempStreak = 1
      }
    }
    longestStreak = Math.max(longestStreak, tempStreak)

    return { currentStreak, longestStreak }
  }

  const checkTodayStatus = (habitRecords: AreaMetricRecord[]) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const todayRecord = habitRecords.find(record => {
      const recordDate = new Date(record.recordedAt)
      recordDate.setHours(0, 0, 0, 0)
      return recordDate.getTime() === today.getTime()
    })
    
    setIsCheckedToday(!!todayRecord)
  }

  const handleCheckIn = async () => {
    if (isCheckedToday) return

    try {
      const result = await databaseApi.createAreaMetricRecord({
        metricId: metric.id,
        value: '1',
        note: 'Daily check-in'
      })

      if (result.success) {
        setIsCheckedToday(true)
        addNotification({
          type: 'success',
          title: 'Habit Completed!',
          message: `Great job completing ${metric.name} today!`
        })
        
        if (onRecordCreated) {
          onRecordCreated(result.data)
        }
        
        // 重新加载数据以更新统计
        loadHabitData()
      }
    } catch (error) {
      console.error('Failed to check in habit:', error)
      addNotification({
        type: 'error',
        title: 'Check-in Failed',
        message: 'Failed to record habit completion'
      })
    }
  }

  const getStreakColor = () => {
    if (stats.currentStreak >= 30) return 'text-purple-600'
    if (stats.currentStreak >= 14) return 'text-green-600'
    if (stats.currentStreak >= 7) return 'text-blue-600'
    if (stats.currentStreak >= 3) return 'text-yellow-600'
    return 'text-gray-600'
  }

  const getCompletionRateColor = () => {
    if (stats.completionRate >= 90) return 'text-green-600'
    if (stats.completionRate >= 75) return 'text-blue-600'
    if (stats.completionRate >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          Loading habit data...
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{metric.name}</CardTitle>
            <CardDescription>Daily Habit Tracker</CardDescription>
          </div>
          <Button
            onClick={handleCheckIn}
            disabled={isCheckedToday}
            variant={isCheckedToday ? "secondary" : "default"}
            size="sm"
            className={cn(
              "transition-all",
              isCheckedToday && "bg-green-100 text-green-700 hover:bg-green-100"
            )}
          >
            {isCheckedToday ? (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Done Today
              </>
            ) : (
              <>
                <Circle className="h-4 w-4 mr-2" />
                Check In
              </>
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 主要统计 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className={cn("text-2xl font-bold flex items-center justify-center gap-1", getStreakColor())}>
              <Flame className="h-5 w-5" />
              {stats.currentStreak}
            </div>
            <p className="text-xs text-muted-foreground">Current Streak</p>
          </div>
          <div className="text-center">
            <div className={cn("text-2xl font-bold", getCompletionRateColor())}>
              {Math.round(stats.completionRate)}%
            </div>
            <p className="text-xs text-muted-foreground">30-Day Rate</p>
          </div>
        </div>

        {/* 进度条 */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Monthly Progress</span>
            <span>{stats.thisMonthCount} days</span>
          </div>
          <Progress value={stats.completionRate} className="h-2" />
        </div>

        {/* 详细统计 */}
        <div className="grid grid-cols-3 gap-2 text-center">
          <div>
            <div className="text-sm font-medium">{stats.thisWeekCount}</div>
            <p className="text-xs text-muted-foreground">This Week</p>
          </div>
          <div>
            <div className="text-sm font-medium">{stats.longestStreak}</div>
            <p className="text-xs text-muted-foreground">Best Streak</p>
          </div>
          <div>
            <div className="text-sm font-medium">{stats.totalCount}</div>
            <p className="text-xs text-muted-foreground">Total</p>
          </div>
        </div>

        {/* 状态标签 */}
        <div className="flex gap-2 flex-wrap">
          {stats.currentStreak >= 7 && (
            <Badge variant="secondary" className="text-xs">
              <Flame className="h-3 w-3 mr-1" />
              On Fire!
            </Badge>
          )}
          {stats.completionRate >= 90 && (
            <Badge variant="secondary" className="text-xs">
              <Target className="h-3 w-3 mr-1" />
              Consistent
            </Badge>
          )}
          {isCheckedToday && (
            <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
              <CheckCircle className="h-3 w-3 mr-1" />
              Completed Today
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
