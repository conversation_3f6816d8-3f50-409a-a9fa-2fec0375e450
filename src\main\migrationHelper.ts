/**
 * 数据库迁移助手
 * 检查和执行必要的数据库迁移
 */

import { PrismaClient } from '@prisma/client'
import fs from 'fs'
import path from 'path'

export class MigrationHelper {
  private prisma: PrismaClient

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
  }

  /**
   * 检查是否需要执行领域指标增强迁移
   */
  async needsAreaMetricEnhancement(): Promise<boolean> {
    try {
      // 尝试查询新字段，如果失败说明需要迁移
      await this.prisma.$queryRaw`SELECT trackingType FROM AreaMetric LIMIT 1`
      return false // 字段存在，不需要迁移
    } catch (error) {
      return true // 字段不存在，需要迁移
    }
  }

  /**
   * 执行领域指标增强迁移
   */
  async executeAreaMetricEnhancement(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('开始执行领域指标增强迁移...')

      // 为AreaMetric表添加新字段
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "trackingType" TEXT NOT NULL DEFAULT 'metric'`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "habitConfig" TEXT`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "standardConfig" TEXT`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT true`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "priority" TEXT`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "category" TEXT`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "description" TEXT`

      // {{ AURA-X: Add - 添加direction字段支持双向KPI. Approval: 寸止(ID:1738157400). }}
      // 添加direction字段
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "direction" TEXT NOT NULL DEFAULT 'increase'`

      // 为ProjectKPI表也添加direction字段
      await this.prisma.$executeRaw`ALTER TABLE "ProjectKPI" ADD COLUMN "direction" TEXT NOT NULL DEFAULT 'increase'`

      // 为AreaMetricRecord表添加新字段
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetricRecord" ADD COLUMN "mood" TEXT`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetricRecord" ADD COLUMN "energy" TEXT`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetricRecord" ADD COLUMN "context" TEXT`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetricRecord" ADD COLUMN "tags" TEXT`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetricRecord" ADD COLUMN "quality" TEXT`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetricRecord" ADD COLUMN "duration" INTEGER`
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetricRecord" ADD COLUMN "difficulty" TEXT`

      // 更新现有数据：将包含习惯关键词的指标标记为habit类型
      await this.prisma.$executeRaw`
        UPDATE "AreaMetric" 
        SET "trackingType" = 'habit' 
        WHERE LOWER("name") LIKE '%daily%' 
           OR LOWER("name") LIKE '%每日%' 
           OR LOWER("name") LIKE '%habit%' 
           OR LOWER("name") LIKE '%习惯%' 
           OR LOWER("name") LIKE '%routine%' 
           OR LOWER("name") LIKE '%例行%'
           OR LOWER("frequency") LIKE '%daily%'
           OR LOWER("frequency") LIKE '%每日%'
      `

      // 为习惯类型的指标设置默认配置
      await this.prisma.$executeRaw`
        UPDATE "AreaMetric" 
        SET "habitConfig" = '{"targetFrequency": 7, "weeklyTarget": 5, "reminderTime": "09:00", "streakGoal": 30}'
        WHERE "trackingType" = 'habit'
      `

      // 创建索引以提高查询性能
      try {
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_tracking_type" ON "AreaMetric"("trackingType")`
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_category" ON "AreaMetric"("category")`
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_priority" ON "AreaMetric"("priority")`
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_active" ON "AreaMetric"("isActive")`
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_record_recorded_at" ON "AreaMetricRecord"("recordedAt")`
      } catch (indexError) {
        console.warn('索引创建失败，但不影响功能:', indexError)
      }

      console.log('领域指标增强迁移完成')
      return { success: true }
    } catch (error) {
      console.error('迁移执行失败:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown migration error' 
      }
    }
  }

  /**
   * 检查并执行所有必要的迁移
   */
  async checkAndExecuteMigrations(): Promise<{ success: boolean; migrations: string[]; errors: string[] }> {
    const executedMigrations: string[] = []
    const errors: string[] = []

    try {
      // 检查领域指标增强迁移
      if (await this.needsAreaMetricEnhancement()) {
        console.log('检测到需要执行领域指标增强迁移')
        const result = await this.executeAreaMetricEnhancement()
        
        if (result.success) {
          executedMigrations.push('AreaMetricEnhancement')
        } else {
          errors.push(`AreaMetricEnhancement: ${result.error}`)
        }
      }

      return {
        success: errors.length === 0,
        migrations: executedMigrations,
        errors
      }
    } catch (error) {
      console.error('迁移检查失败:', error)
      return {
        success: false,
        migrations: executedMigrations,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }

  /**
   * 回滚领域指标增强迁移（仅用于开发测试）
   */
  async rollbackAreaMetricEnhancement(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('开始回滚领域指标增强迁移...')

      // 删除AreaMetric表的新字段
      const areaMetricColumns = [
        'trackingType', 'habitConfig', 'standardConfig', 
        'isActive', 'priority', 'category', 'description'
      ]

      for (const column of areaMetricColumns) {
        try {
          await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" DROP COLUMN "${column}"`
        } catch (error) {
          console.warn(`删除列 ${column} 失败:`, error)
        }
      }

      // 删除AreaMetricRecord表的新字段
      const recordColumns = [
        'mood', 'energy', 'context', 'tags', 
        'quality', 'duration', 'difficulty'
      ]

      for (const column of recordColumns) {
        try {
          await this.prisma.$executeRaw`ALTER TABLE "AreaMetricRecord" DROP COLUMN "${column}"`
        } catch (error) {
          console.warn(`删除列 ${column} 失败:`, error)
        }
      }

      console.log('领域指标增强迁移回滚完成')
      return { success: true }
    } catch (error) {
      console.error('迁移回滚失败:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown rollback error' 
      }
    }
  }

  /**
   * 获取数据库schema信息
   */
  async getDatabaseInfo(): Promise<{
    areaMetricColumns: string[]
    areaMetricRecordColumns: string[]
    hasEnhancedFields: boolean
  }> {
    try {
      // 获取AreaMetric表的列信息
      const areaMetricInfo = await this.prisma.$queryRaw<Array<{ name: string }>>`
        PRAGMA table_info(AreaMetric)
      `
      
      const areaMetricRecordInfo = await this.prisma.$queryRaw<Array<{ name: string }>>`
        PRAGMA table_info(AreaMetricRecord)
      `

      const areaMetricColumns = areaMetricInfo.map(col => col.name)
      const areaMetricRecordColumns = areaMetricRecordInfo.map(col => col.name)

      const hasEnhancedFields = areaMetricColumns.includes('trackingType')

      return {
        areaMetricColumns,
        areaMetricRecordColumns,
        hasEnhancedFields
      }
    } catch (error) {
      console.error('获取数据库信息失败:', error)
      return {
        areaMetricColumns: [],
        areaMetricRecordColumns: [],
        hasEnhancedFields: false
      }
    }
  }
}

export default MigrationHelper
