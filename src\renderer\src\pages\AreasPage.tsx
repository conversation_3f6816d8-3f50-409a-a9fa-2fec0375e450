import { Outlet, useLocation } from 'react-router-dom'
import { useState, useMemo } from 'react'
import { <PERSON>Header, PageHeaderActions, EmptyStates } from '../components/shared'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { Badge } from '../components/ui/badge'
import AreaCard from '../components/features/AreaCard'
import CreateAreaDialog from '../components/features/CreateAreaDialog'
import { useConfirmDialog } from '../components/shared/ConfirmDialog'
import { useAreaStore } from '../store/areaStore'
import { useProjectStore } from '../store/projectStore'
// {{ AURA-X: Add - 导入数据库API用于保存Area. Approval: 寸止(ID:1738157400). }}
import { databaseApi } from '../lib/api'
import { useLanguage } from '../contexts/LanguageContext'
import { cn } from '../lib/utils'
import type { Area } from '../../../shared/types'

export function AreasPage() {
  const location = useLocation()
  const isDetailView = location.pathname !== '/areas'

  // Always call all hooks before any conditional returns
  const { areas, addArea, updateArea, deleteArea, archiveArea } = useAreaStore()
  const { projects } = useProjectStore()
  const { t } = useLanguage()
  const { confirm, ConfirmDialog: ConfirmDialogComponent } = useConfirmDialog()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingArea, setEditingArea] = useState<Area | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'name' | 'status' | 'updated'>('updated')

  // Filter and sort areas - always calculate even for detail view
  const filteredAreas = useMemo(() => {
    let filtered = areas.filter((area) => !area.archived)

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (area) =>
          area.name.toLowerCase().includes(query) ||
          area.description?.toLowerCase().includes(query) ||
          area.standard?.toLowerCase().includes(query)
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter((area) => (area.status || 'Active') === statusFilter)
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'status':
          return (a.status || 'Active').localeCompare(b.status || 'Active')
        case 'updated':
        default:
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      }
    })

    return filtered
  }, [areas, searchQuery, statusFilter, sortBy])

  // Calculate related projects count for each area
  const getRelatedProjectsCount = (areaId: string) => {
    return projects.filter((project) => project.areaId === areaId && !project.archived).length
  }

  // Calculate habit completion rate (placeholder - would be calculated from actual habit data)
  const getHabitCompletionRate = (area: Area) => {
    if (!(area as any).habits || (area as any).habits.length === 0) return 0
    // This would be calculated from actual habit tracking data
    return Math.floor(Math.random() * 100) // Placeholder
  }

  const handleCreateArea = async (areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      // {{ AURA-X: Modify - 同时保存到数据库和本地状态. Approval: 寸止(ID:1738157400). }}
      // 先保存到数据库
      const result = await databaseApi.createArea({
        name: areaData.name,
        description: areaData.description,
        color: areaData.color,
        icon: areaData.icon,
        standard: areaData.standard,
        status: areaData.status,
        reviewFrequency: areaData.reviewFrequency,
        archived: areaData.archived
      })

      if (result.success) {
        // 使用数据库返回的 Area 数据（包含正确的 ID）
        const dbArea = result.data
        const newArea: Area = {
          id: dbArea.id,
          name: dbArea.name,
          description: dbArea.description,
          standard: areaData.standard,
          status: areaData.status,
          reviewFrequency: areaData.reviewFrequency,
          archived: areaData.archived,
          color: dbArea.color,
          icon: dbArea.icon,
          createdAt: new Date(dbArea.createdAt),
          updatedAt: new Date(dbArea.updatedAt)
        }
        addArea(newArea)
      } else {
        console.error('Failed to create area in database:', result.error)
        // 如果数据库保存失败，仍然保存到本地状态（离线模式）
        const newArea: Area = {
          ...areaData,
          id: `area-${Date.now()}`,
          createdAt: new Date(),
          updatedAt: new Date()
        }
        addArea(newArea)
      }
    } catch (error) {
      console.error('Error creating area:', error)
      // 错误处理：保存到本地状态
      const newArea: Area = {
        ...areaData,
        id: `area-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      addArea(newArea)
    }
  }

  const handleEditArea = async (areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingArea) {
      updateArea(editingArea.id, {
        ...areaData,
        updatedAt: new Date()
      })
      setEditingArea(null)
    }
  }

  const handleDeleteArea = (area: Area) => {
    confirm({
      title: '删除领域',
      description: `确定要删除"${area.name}"吗？此操作无法撤销。`,
      variant: 'destructive',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm: () => {
        deleteArea(area.id)
      }
    })
  }

  const handleArchiveArea = (area: Area) => {
    confirm({
      title: '归档领域',
      description: `归档"${area.name}"？您可以稍后从归档中恢复。`,
      variant: 'warning',
      confirmText: '归档',
      cancelText: '取消',
      onConfirm: () => {
        archiveArea(area.id)
      }
    })
  }

  const statusOptions = [
    { value: 'all', label: t('pages.areas.allStatus') },
    { value: 'Active', label: t('pages.areas.filters.status.active') },
    { value: 'Needs Attention', label: t('pages.areas.filters.status.needsAttention') },
    { value: 'On Hold', label: t('pages.areas.filters.status.onHold') },
    { value: 'Review Required', label: t('pages.areas.filters.status.reviewRequired') }
  ]

  const sortOptions = [
    { value: 'updated', label: t('pages.areas.filters.sort.updated') },
    { value: 'name', label: t('pages.areas.filters.sort.name') },
    { value: 'status', label: t('pages.areas.filters.sort.status') }
  ]

  // Return detail view after all hooks have been called
  if (isDetailView) {
    return <Outlet />
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader
        title={t('pages.areas.title')}
        description={t('pages.areas.description')}
        badge={{ text: 'P.A.R.A.', variant: 'secondary' }}
        actions={
          <PageHeaderActions.Create onClick={() => setIsCreateDialogOpen(true)}>
            {t('pages.areas.newArea')}
          </PageHeaderActions.Create>
        }
        className="border-l-4 border-area pl-6"
      />

      {areas.filter((a) => !a.archived).length === 0 ? (
        <div className="max-w-md mx-auto mt-12">
          <EmptyStates.Areas onCreate={() => setIsCreateDialogOpen(true)} />
        </div>
      ) : (
        <>
          {/* Filters and Controls */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-3 flex-1">
              <Input
                placeholder={t('pages.areas.searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="sm:max-w-xs"
              />

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="sm:w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="sm:w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <div className="flex items-center border rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-7 px-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                    />
                  </svg>
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-7 px-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 10h16M4 14h16M4 18h16"
                    />
                  </svg>
                </Button>
              </div>

              <Badge variant="outline" className="text-xs">
                {filteredAreas.length} {t('nav.areas').toLowerCase()}
              </Badge>
            </div>
          </div>

          {/* Areas Grid/List */}
          {filteredAreas.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <div className="text-4xl mb-2">🔍</div>
              <p className="text-sm">{t('components.emptyStates.noItemsFound')}</p>
              <p className="text-xs mt-1">{t('components.emptyStates.tryAdjustingFilters')}</p>
            </div>
          ) : (
            <div
              className={cn(
                'grid gap-6',
                viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'
              )}
            >
              {filteredAreas.map((area) => (
                <AreaCard
                  key={area.id}
                  area={area}
                  onEdit={setEditingArea}
                  onDelete={handleDeleteArea}
                  onArchive={handleArchiveArea}
                  relatedProjectsCount={getRelatedProjectsCount(area.id)}
                  habitCompletionRate={getHabitCompletionRate(area)}
                  className={viewMode === 'list' ? 'max-w-none' : ''}
                />
              ))}
            </div>
          )}
        </>
      )}

      {/* Create/Edit Area Dialog */}
      <CreateAreaDialog
        isOpen={isCreateDialogOpen || !!editingArea}
        onClose={() => {
          setIsCreateDialogOpen(false)
          setEditingArea(null)
        }}
        onSubmit={editingArea ? handleEditArea : handleCreateArea}
        initialData={editingArea || undefined}
      />

      {/* Confirm Dialog */}
      <ConfirmDialogComponent />
    </div>
  )
}

export default AreasPage
