import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { Area, AreaMetric, Habit, HabitRecord } from '../../../shared/types'
import { databaseApi } from '../lib/api'

export interface AreaState {
  areas: Area[]
  currentArea: Area | null
  habits: Habit[]
  habitRecords: HabitRecord[]
  metrics: AreaMetric[]
  loading: boolean
  error: string | null
}

export interface AreaActions {
  // Area CRUD operations
  setAreas: (areas: Area[]) => void
  addArea: (area: Area) => void
  updateArea: (id: string, updates: Partial<Area>) => void
  deleteArea: (id: string) => void
  archiveArea: (id: string) => void

  // Current area management
  setCurrentArea: (area: Area | null) => void

  // Habit management
  setHabits: (habits: Habit[]) => void
  addHabit: (habit: Habit) => void
  updateHabit: (id: string, updates: Partial<Habit>) => void
  deleteHabit: (id: string) => void

  // Habit record management
  setHabitRecords: (records: HabitRecord[]) => void
  addHabitRecord: (record: HabitRecord) => void
  updateHabitRecord: (id: string, updates: Partial<HabitRecord>) => void
  toggleHabitRecord: (habitId: string, date: Date) => void

  // Metrics management
  setMetrics: (metrics: AreaMetric[]) => void
  addMetric: (metric: AreaMetric) => void
  updateMetric: (id: string, updates: Partial<AreaMetric>) => void
  deleteMetric: (id: string) => void

  // Loading and error states
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // Async operations
  fetchAreas: () => Promise<void>
  createArea: (data: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  fetchHabitsForArea: (areaId: string) => Promise<void>
}

export type AreaStore = AreaState & AreaActions

export const useAreaStore = create<AreaStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        areas: [],
        currentArea: null,
        habits: [],
        habitRecords: [],
        metrics: [],
        loading: false,
        error: null,

        // Area actions
        setAreas: (areas) => set({ areas }),

        addArea: (area) =>
          set((state) => ({
            areas: [area, ...state.areas]
          })),

        updateArea: (id, updates) =>
          set((state) => ({
            areas: state.areas.map((area) => (area.id === id ? { ...area, ...updates } : area)),
            currentArea:
              state.currentArea?.id === id
                ? { ...state.currentArea, ...updates }
                : state.currentArea
          })),

        deleteArea: (id) =>
          set((state) => ({
            areas: state.areas.filter((area) => area.id !== id),
            currentArea: state.currentArea?.id === id ? null : state.currentArea
          })),

        archiveArea: (id) =>
          set((state) => ({
            areas: state.areas.map((area) => (area.id === id ? { ...area, archived: true } : area))
          })),

        setCurrentArea: (area) => set({ currentArea: area }),

        // Habit actions
        setHabits: (habits) => set({ habits }),

        addHabit: (habit) =>
          set((state) => ({
            habits: [habit, ...state.habits]
          })),

        updateHabit: (id, updates) =>
          set((state) => ({
            habits: state.habits.map((habit) =>
              habit.id === id ? { ...habit, ...updates } : habit
            )
          })),

        deleteHabit: (id) =>
          set((state) => ({
            habits: state.habits.filter((habit) => habit.id !== id)
          })),

        // Habit record actions
        setHabitRecords: (records) => set({ habitRecords: records }),

        addHabitRecord: (record) =>
          set((state) => ({
            habitRecords: [record, ...state.habitRecords]
          })),

        updateHabitRecord: (id, updates) =>
          set((state) => ({
            habitRecords: state.habitRecords.map((record) =>
              record.id === id ? { ...record, ...updates } : record
            )
          })),

        toggleHabitRecord: (habitId, date) => {
          const dateStr = date.toISOString().split('T')[0]
          const existingRecord = get().habitRecords.find(
            (record) =>
              record.habitId === habitId && record.date.toISOString().split('T')[0] === dateStr
          )

          if (existingRecord) {
            get().updateHabitRecord(existingRecord.id, {
              completed: !existingRecord.completed
            })
          } else {
            // Create new record
            const newRecord: HabitRecord = {
              id: `temp-${Date.now()}`, // Temporary ID
              habitId,
              date,
              completed: true
            }
            get().addHabitRecord(newRecord)
          }
        },

        // Metrics actions
        setMetrics: (metrics) => set({ metrics }),

        addMetric: (metric) =>
          set((state) => ({
            metrics: [metric, ...state.metrics]
          })),

        updateMetric: (id, updates) =>
          set((state) => ({
            metrics: state.metrics.map((metric) =>
              metric.id === id ? { ...metric, ...updates } : metric
            )
          })),

        deleteMetric: (id) =>
          set((state) => ({
            metrics: state.metrics.filter((metric) => metric.id !== id)
          })),

        // Common actions
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),

        // Async operations
        fetchAreas: async () => {
          set({ loading: true, error: null })
          try {
            const result = await databaseApi.getAreas()
            if (result.success) {
              // Convert database areas to frontend Area type
              const areas: Area[] =
                result.data?.map((dbArea: any) => ({
                  id: dbArea.id,
                  name: dbArea.name,
                  description: dbArea.description,
                  standard: dbArea.standard,
                  icon: dbArea.icon,
                  color: dbArea.color,
                  status: dbArea.status || 'Active',
                  reviewFrequency: dbArea.reviewFrequency || 'Weekly',
                  archived: dbArea.archived || false,
                  createdAt: new Date(dbArea.createdAt),
                  updatedAt: new Date(dbArea.updatedAt)
                })) || []
              set({ areas, loading: false })
            } else {
              set({ error: result.error, loading: false })
            }
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch areas',
              loading: false
            })
          }
        },

        createArea: async (_data) => {
          set({ loading: true, error: null })
          try {
            // Will be implemented with IPC
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to create area',
              loading: false
            })
          }
        },

        fetchHabitsForArea: async (_areaId) => {
          set({ loading: true, error: null })
          try {
            // Will be implemented with IPC
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch habits',
              loading: false
            })
          }
        }
      }),
      {
        name: 'area-store',
        partialize: (state) => ({
          areas: state.areas,
          currentArea: state.currentArea,
          habits: state.habits,
          habitRecords: state.habitRecords,
          metrics: state.metrics
        })
      }
    ),
    {
      name: 'area-store'
    }
  )
)
