import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '../ui/tabs'
import AreaMetricQuickInput from './AreaMetricQuickInput'
import AreaMetricHistory from './AreaMetricHistory'
import CreateAreaMetricDialog from './CreateAreaMetricDialog'
// {{ AURA-X: Add - 导入增强版创建对话框. Approval: 寸止(ID:1738157400). }}
import EnhancedCreateAreaMetricDialog from './EnhancedCreateAreaMetricDialog'
import BatchAreaMetricRecordDialog from './BatchAreaMetricRecordDialog'
// {{ AURA-X: Add - 复用项目模块的高级KPI组件. Approval: 寸止(ID:1738157400). }}
import KPIDashboard from './KPIDashboard'
import KPIChart from './KPIChart'
import KPITrends from './KPITrends'
// {{ AURA-X: Add - 导入习惯追踪组件. Approval: 寸止(ID:1738157400). }}
import HabitTracker from './HabitTracker'
// {{ AURA-X: Add - 导入高级分析组件. Approval: 寸止(ID:1738157400). }}
import AreaAnalytics from './AreaAnalytics'
// {{ AURA-X: Add - 导入批量方向更新组件. Approval: 寸止(ID:1738157400). }}
import MetricDirectionBatchUpdate from './MetricDirectionBatchUpdate'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
// {{ AURA-X: Add - 导入领域指标适配器. Approval: 寸止(ID:1738157400). }}
import {
  adaptAreaMetricsToProjectKPIs,
  getHabitMetrics,
  getRegularMetrics
} from '../../lib/areaMetricAdapter'
// {{ AURA-X: Add - 导入缓存服务. Approval: 寸止(ID:1738157400). }}
import { areaMetricCache } from '../../lib/areaMetricCache'
import type { AreaMetric, AreaMetricRecord } from '../../../../shared/types'

interface AreaKPIManagementProps {
  areaId: string
}

export function AreaKPIManagement({ areaId }: AreaKPIManagementProps) {
  const [metrics, setMetrics] = useState<AreaMetric[]>([])
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  // {{ AURA-X: Add - 增强版对话框状态. Approval: 寸止(ID:1738157400). }}
  const [isEnhancedDialogOpen, setIsEnhancedDialogOpen] = useState(false)
  const [isBatchRecordDialogOpen, setIsBatchRecordDialogOpen] = useState(false)
  const [editingMetric, setEditingMetric] = useState<AreaMetric | null>(null)
  const [loading, setLoading] = useState(true)
  const { addNotification } = useUIStore()

  // {{ AURA-X: Add - 将领域指标适配为项目KPI格式以复用组件. Approval: 寸止(ID:1738157400). }}
  const adaptedKPIs = adaptAreaMetricsToProjectKPIs(metrics)

  // {{ AURA-X: Add - 分离习惯类型和常规指标. Approval: 寸止(ID:1738157400). }}
  const habitMetrics = getHabitMetrics(metrics)
  const regularMetrics = getRegularMetrics(metrics)

  useEffect(() => {
    loadMetrics()
  }, [areaId])

  const loadMetrics = async () => {
    setLoading(true)
    try {
      // {{ AURA-X: Add - 使用缓存服务优化数据加载. Approval: 寸止(ID:1738157400). }}
      // 首先尝试从缓存获取
      const cachedMetrics = areaMetricCache.getCachedAreaMetrics(areaId)
      if (cachedMetrics) {
        setMetrics(cachedMetrics)
        setLoading(false)

        // 后台刷新数据
        const result = await databaseApi.getAreaMetrics(areaId)
        if (result.success) {
          const freshMetrics = result.data || []
          areaMetricCache.cacheAreaMetrics(areaId, freshMetrics)
          setMetrics(freshMetrics)
        }
        return
      }

      // 缓存未命中，从数据库加载
      const result = await databaseApi.getAreaMetrics(areaId)
      if (result.success) {
        const metrics = result.data || []
        setMetrics(metrics)
        areaMetricCache.cacheAreaMetrics(areaId, metrics)

        // 预加载数据
        areaMetricCache.preloadAreaData(areaId, databaseApi)
      } else {
        throw new Error(result.error || 'Failed to load metrics')
      }
    } catch (error) {
      console.error('Failed to load area metrics:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Load Metrics',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateMetric = async (metric: Omit<AreaMetric, 'id' | 'updatedAt' | 'areaId'>) => {
    try {
      const result = await databaseApi.createAreaMetric({
        areaId,
        ...metric
      })

      if (result.success) {
        setMetrics(prev => [result.data, ...prev])
        addNotification({
          type: 'success',
          title: 'Metric Created',
          message: `${metric.name} has been created successfully`
        })
      } else {
        throw new Error(result.error || 'Failed to create metric')
      }
    } catch (error) {
      console.error('Failed to create area metric:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Create Metric',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleUpdateMetric = async (metric: AreaMetric) => {
    if (!editingMetric) return

    try {
      const result = await databaseApi.updateAreaMetric({
        id: metric.id,
        updates: {
          name: metric.name,
          value: metric.value,
          target: metric.target,
          unit: metric.unit,
          frequency: metric.frequency
        }
      })

      if (result.success) {
        setMetrics(prev => prev.map(m => m.id === metric.id ? result.data : m))
        addNotification({
          type: 'success',
          title: 'Metric Updated',
          message: `${metric.name} has been updated successfully`
        })
      } else {
        throw new Error(result.error || 'Failed to update metric')
      }
    } catch (error) {
      console.error('Failed to update area metric:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Update Metric',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleDeleteMetric = async (metric: AreaMetric) => {
    try {
      const result = await databaseApi.deleteAreaMetric(metric.id)
      if (result.success) {
        setMetrics(prev => prev.filter(m => m.id !== metric.id))
        addNotification({
          type: 'success',
          title: 'Metric Deleted',
          message: `${metric.name} has been deleted successfully`
        })
      } else {
        throw new Error(result.error || 'Failed to delete metric')
      }
    } catch (error) {
      console.error('Failed to delete area metric:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Delete Metric',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleEditMetric = (metric: AreaMetric) => {
    setEditingMetric(metric)
    setIsCreateDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setIsCreateDialogOpen(false)
    setEditingMetric(null)
  }

  const handleRecordCreated = (record: AreaMetricRecord) => {
    // 当创建新记录时，更新对应Metric的当前值
    setMetrics(prev => prev.map(metric =>
      metric.id === record.metricId
        ? { ...metric, value: record.value, updatedAt: record.recordedAt }
        : metric
    ))

    // {{ AURA-X: Add - 智能更新缓存. Approval: 寸止(ID:1738157400). }}
    areaMetricCache.smartCacheUpdate(areaId, record.metricId, record)
  }

  const handleBatchRecordsCreated = (records: AreaMetricRecord[]) => {
    // 批量更新Metric的当前值
    setMetrics(prev => prev.map(metric => {
      const record = records.find(r => r.metricId === metric.id)
      return record 
        ? { ...metric, value: record.value, updatedAt: record.recordedAt }
        : metric
    }))
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <div className="text-muted-foreground">Loading metrics...</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Key Metrics
              </CardTitle>
              <CardDescription>
                Track and manage your area's key performance indicators
              </CardDescription>
            </div>
            <div className="flex gap-2">
              {metrics.length > 0 && (
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => setIsBatchRecordDialogOpen(true)}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                  Batch Record
                </Button>
              )}
              {/* {{ AURA-X: Modify - 暂时隐藏增强版按钮，等待数据库迁移. Approval: 寸止(ID:1738157400). }} */}
              {false && (
                <Button size="sm" onClick={() => setIsEnhancedDialogOpen(true)}>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Add Enhanced
                </Button>
              )}
              <Button size="sm" variant="outline" onClick={() => setIsCreateDialogOpen(true)}>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add Basic
              </Button>

              {/* {{ AURA-X: Add - 批量方向更新按钮. Approval: 寸止(ID:1738157400). }} */}
              <MetricDirectionBatchUpdate
                areaId={areaId}
                onUpdate={loadMetrics}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            {/* {{ AURA-X: Modify - 扩展为6个标签页，添加高级分析. Approval: 寸止(ID:1738157400). }} */}
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="records">Records</TabsTrigger>
              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger value="charts">Charts</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {metrics.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8 text-muted-foreground">
                    <div className="text-4xl mb-2">📊</div>
                    <p className="text-sm">No metrics yet</p>
                    <p className="text-xs mt-1">Create your first metric to start tracking progress</p>
                  </CardContent>
                </Card>
              ) : (
                <>
                  {/* {{ AURA-X: Add - 习惯追踪区域. Approval: 寸止(ID:1738157400). }} */}
                  {habitMetrics.length > 0 && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-semibold">🎯 Daily Habits</h3>
                        <Badge variant="secondary">{habitMetrics.length}</Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {habitMetrics.map((metric) => (
                          <HabitTracker
                            key={metric.id}
                            metric={metric}
                            onRecordCreated={handleRecordCreated}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* {{ AURA-X: Add - 常规指标区域. Approval: 寸止(ID:1738157400). }} */}
                  {regularMetrics.length > 0 && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-semibold">📊 Key Metrics</h3>
                        <Badge variant="secondary">{regularMetrics.length}</Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {regularMetrics.map((metric) => (
                          <AreaMetricQuickInput
                            key={metric.id}
                            metric={metric}
                            onRecordCreated={handleRecordCreated}
                            onEdit={handleEditMetric}
                            onDelete={handleDeleteMetric}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
            </TabsContent>

            <TabsContent value="records" className="space-y-4">
              {metrics.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8 text-muted-foreground">
                    <div className="text-4xl mb-2">📝</div>
                    <p className="text-sm">No metrics to track</p>
                    <p className="text-xs mt-1">Create metrics first to start recording data</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {metrics.map((metric) => (
                    <AreaMetricHistory key={metric.id} metric={metric} />
                  ))}
                </div>
              )}
            </TabsContent>

            {/* {{ AURA-X: Add - 复用项目模块的Dashboard组件. Approval: 寸止(ID:1738157400). }} */}
            <TabsContent value="dashboard" className="space-y-4">
              <KPIDashboard kpis={adaptedKPIs} />
            </TabsContent>

            {/* {{ AURA-X: Add - 复用项目模块的Charts组件. Approval: 寸止(ID:1738157400). }} */}
            <TabsContent value="charts" className="space-y-4">
              <KPIChart kpis={adaptedKPIs} />
            </TabsContent>

            {/* {{ AURA-X: Add - 复用项目模块的Trends组件. Approval: 寸止(ID:1738157400). }} */}
            <TabsContent value="trends" className="space-y-4">
              <KPITrends kpis={adaptedKPIs} />
            </TabsContent>

            {/* {{ AURA-X: Add - 高级分析标签页. Approval: 寸止(ID:1738157400). }} */}
            <TabsContent value="analytics" className="space-y-4">
              <AreaAnalytics areaId={areaId} metrics={metrics} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Create/Edit Metric Dialog */}
      <CreateAreaMetricDialog
        isOpen={isCreateDialogOpen}
        onClose={handleCloseDialog}
        onSubmit={editingMetric ? handleUpdateMetric : handleCreateMetric}
        initialData={editingMetric}
        areaId={areaId}
      />

      {/* {{ AURA-X: Add - 增强版创建对话框. Approval: 寸止(ID:1738157400). }} */}
      <EnhancedCreateAreaMetricDialog
        isOpen={isEnhancedDialogOpen}
        onClose={() => {
          setIsEnhancedDialogOpen(false)
          setEditingMetric(null)
        }}
        onSubmit={editingMetric ? handleUpdateMetric : handleCreateMetric}
        initialData={editingMetric}
        areaId={areaId}
      />

      {/* Batch Record Dialog */}
      <BatchAreaMetricRecordDialog
        isOpen={isBatchRecordDialogOpen}
        onClose={() => setIsBatchRecordDialogOpen(false)}
        metrics={metrics}
        onRecordsCreated={handleBatchRecordsCreated}
      />
    </>
  )
}

export default AreaKPIManagement
