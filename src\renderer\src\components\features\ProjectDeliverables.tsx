import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Plus, MoreHorizontal, Calendar, Target, CheckCircle, Clock, AlertCircle } from 'lucide-react'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import { cn } from '../../lib/utils'
import type { Deliverable } from '../../../../shared/types'
import CreateDeliverableDialog from './CreateDeliverableDialog'

interface ProjectDeliverablesProps {
  projectId: string
}

export function ProjectDeliverables({ projectId }: ProjectDeliverablesProps) {
  const [deliverables, setDeliverables] = useState<Deliverable[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const { addNotification } = useUIStore()
  const { confirm: showConfirmDialog, ConfirmDialog } = useConfirmDialog()

  // Load deliverables
  useEffect(() => {
    loadDeliverables()
  }, [projectId])

  const loadDeliverables = async () => {
    try {
      setLoading(true)
      const result = await databaseApi.getProjectDeliverables(projectId)
      if (result.success) {
        setDeliverables(result.data || [])
      } else {
        throw new Error(result.error || 'Failed to load deliverables')
      }
    } catch (error) {
      console.error('Failed to load deliverables:', error)
      addNotification({
        type: 'error',
        title: 'Failed to load deliverables',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateDeliverable = async (data: any) => {
    try {
      const result = await databaseApi.createDeliverable({
        ...data,
        projectId
      })
      if (result.success) {
        setDeliverables(prev => [result.data, ...prev])
        addNotification({
          type: 'success',
          title: 'Deliverable created',
          message: `"${data.title}" has been created successfully`
        })
      } else {
        throw new Error(result.error || 'Failed to create deliverable')
      }
    } catch (error) {
      console.error('Failed to create deliverable:', error)
      addNotification({
        type: 'error',
        title: 'Failed to create deliverable',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleUpdateStatus = async (deliverable: Deliverable, newStatus: string) => {
    try {
      const updates: any = { status: newStatus }
      
      // Set actual date when completing
      if (newStatus === 'completed' && deliverable.status !== 'completed') {
        updates.actualDate = new Date()
      }

      const result = await databaseApi.updateDeliverable({
        id: deliverable.id,
        updates
      })
      
      if (result.success) {
        setDeliverables(prev => 
          prev.map(d => d.id === deliverable.id ? result.data : d)
        )
        addNotification({
          type: 'success',
          title: 'Status updated',
          message: `"${deliverable.title}" status updated to ${newStatus}`
        })
      } else {
        throw new Error(result.error || 'Failed to update status')
      }
    } catch (error) {
      console.error('Failed to update status:', error)
      addNotification({
        type: 'error',
        title: 'Failed to update status',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleDeleteDeliverable = (deliverable: Deliverable) => {
    showConfirmDialog({
      title: 'Delete Deliverable',
      message: `Are you sure you want to delete "${deliverable.title}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      variant: 'destructive',
      onConfirm: async () => {
        try {
          const result = await databaseApi.deleteDeliverable(deliverable.id)
          if (result.success) {
            setDeliverables(prev => prev.filter(d => d.id !== deliverable.id))
            addNotification({
              type: 'success',
              title: 'Deliverable deleted',
              message: `"${deliverable.title}" has been deleted`
            })
          } else {
            throw new Error(result.error || 'Failed to delete deliverable')
          }
        } catch (error) {
          console.error('Failed to delete deliverable:', error)
          addNotification({
            type: 'error',
            title: 'Failed to delete deliverable',
            message: error instanceof Error ? error.message : 'Unknown error occurred'
          })
        }
      }
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'delivered':
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />
      case 'in_progress':
        return <Clock className="h-4 w-4" />
      case 'planned':
        return <Target className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'delivered':
      case 'accepted':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'planned':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    }
  }

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatDate = (date: string | Date | null) => {
    if (!date) return null
    return new Date(date).toLocaleDateString()
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Project Deliverables</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Project Deliverables</CardTitle>
              <CardDescription>
                Track and manage project outcomes and deliverables
              </CardDescription>
            </div>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Deliverable
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {deliverables.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <div className="text-4xl mb-2">🎯</div>
              <p className="text-sm">No deliverables yet</p>
              <p className="text-xs mt-1">Add your first deliverable to start tracking project outcomes</p>
            </div>
          ) : (
            <div className="space-y-4">
              {deliverables.map((deliverable) => (
                <div
                  key={deliverable.id}
                  className="flex items-start space-x-4 p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium text-sm truncate">
                        {deliverable.title}
                      </h4>
                      <Badge variant="outline" className={cn("text-xs", getStatusColor(deliverable.status))}>
                        {getStatusIcon(deliverable.status)}
                        <span className="ml-1 capitalize">{deliverable.status.replace('_', ' ')}</span>
                      </Badge>
                      {deliverable.priority && (
                        <Badge variant="outline" className={cn("text-xs", getPriorityColor(deliverable.priority))}>
                          {deliverable.priority}
                        </Badge>
                      )}
                      <Badge variant="outline" className="text-xs">
                        {deliverable.type}
                      </Badge>
                    </div>
                    {deliverable.description && (
                      <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                        {deliverable.description}
                      </p>
                    )}
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      {deliverable.deadline && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>Due: {formatDate(deliverable.deadline)}</span>
                        </div>
                      )}
                      {deliverable.actualDate && (
                        <div className="flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          <span>Completed: {formatDate(deliverable.actualDate)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleUpdateStatus(deliverable, 'in_progress')}>
                        Start Progress
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleUpdateStatus(deliverable, 'completed')}>
                        Mark Complete
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleUpdateStatus(deliverable, 'delivered')}>
                        Mark Delivered
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDeleteDeliverable(deliverable)}
                        className="text-destructive"
                      >
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Deliverable Dialog */}
      <CreateDeliverableDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSubmit={handleCreateDeliverable}
      />

      {/* Confirm Dialog */}
      <ConfirmDialog />
    </>
  )
}

export default ProjectDeliverables
