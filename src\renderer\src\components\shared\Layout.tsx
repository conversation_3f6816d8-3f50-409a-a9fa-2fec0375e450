import { Outlet, useLocation } from 'react-router-dom'
import { useEffect, useState } from 'react'
import Navigation from './Navigation'
import Breadcrumbs from './Breadcrumbs'
import { ErrorBoundary } from './ErrorBoundary'
import { ToastContainer } from '../ui/toast'
import { useUIStore } from '../../store/uiStore'

export function Layout() {
  const location = useLocation()
  const [isTransitioning, setIsTransitioning] = useState(false)
  const { notifications, removeNotification } = useUIStore()

  // Handle page transitions
  useEffect(() => {
    setIsTransitioning(true)
    const timer = setTimeout(() => setIsTransitioning(false), 150)
    return () => clearTimeout(timer)
  }, [location.pathname])

  return (
    <div className="flex h-screen bg-background">
      {/* Left Sidebar Navigation */}
      <aside className="w-64 sidebar-layout flex-shrink-0 scrollbar-hidden overflow-y-auto">
        <Navigation />
      </aside>

      {/* Main Content Area */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar with Breadcrumbs */}
        <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center justify-between px-6 py-3">
            <Breadcrumbs />

            {/* Window Controls (for Electron) */}
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => window.electronAPI?.window.minimize()}
                className="w-8 h-8 rounded border border-gray-400 bg-gray-200 hover:bg-gray-300 transition-colors"
                title="最小化"
              />
              <button
                type="button"
                onClick={() => window.electronAPI?.window.maximize()}
                className="w-8 h-8 rounded border border-gray-400 bg-gray-200 hover:bg-gray-300 transition-colors"
                title="最大化"
              />
              <button
                type="button"
                onClick={() => window.electronAPI?.window.close()}
                className="w-8 h-8 rounded border border-gray-400 bg-gray-200 hover:bg-gray-300 transition-colors"
                title="关闭"
              />
            </div>
          </div>
        </header>

        {/* Page Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto scrollbar-hidden">
            <ErrorBoundary>
              <div
                className={`transition-all duration-200 ${
                  isTransitioning
                    ? 'opacity-0 transform translate-y-2'
                    : 'opacity-100 transform translate-y-0'
                }`}
              >
                <Outlet />
              </div>
            </ErrorBoundary>
          </div>
        </div>
      </main>

      {/* Toast Notifications */}
      <ToastContainer notifications={notifications} onRemove={removeNotification} />
    </div>
  )
}

export default Layout
