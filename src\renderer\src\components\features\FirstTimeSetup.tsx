import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog'
import { useUserSettingsStore } from '../../store/userSettingsStore'
import { useUIStore } from '../../store/uiStore'
import { fileSystemApi } from '../../lib/api'

interface FirstTimeSetupProps {
  isOpen: boolean
  onComplete: () => void
}

export function FirstTimeSetup({ isOpen, onComplete }: FirstTimeSetupProps) {
  const [step, setStep] = useState(1)
  const [username, setUsername] = useState('')
  const [workspaceDirectory, setWorkspaceDirectory] = useState('')
  const [isSelectingDirectory, setIsSelectingDirectory] = useState(false)

  const { updateSettings, completeFirstTimeSetup } = useUserSettingsStore()
  const { addNotification } = useUIStore()

  const handleSelectDirectory = async () => {
    setIsSelectingDirectory(true)
    try {
      // 检查是否在Electron环境中
      if (window.electronAPI?.app?.showOpenDialog) {
        const result = await window.electronAPI.app.showOpenDialog({
          properties: ['openDirectory'],
          title: '选择工作目录',
          buttonLabel: '选择此目录'
        })

        if (!result.canceled && result.filePaths.length > 0) {
          setWorkspaceDirectory(result.filePaths[0])
        }
      } else {
        // 浏览器环境：使用默认目录
        const defaultDir = './PaoLife-Workspace'
        setWorkspaceDirectory(defaultDir)

        addNotification({
          type: 'info',
          title: '使用默认目录',
          message: `浏览器环境下使用默认工作目录: ${defaultDir}`
        })
      }
    } catch (error) {
      // 浏览器环境回退方案
      const defaultDir = './PaoLife-Workspace'
      setWorkspaceDirectory(defaultDir)

      addNotification({
        type: 'warning',
        title: '使用默认目录',
        message: `无法打开目录选择器，使用默认目录: ${defaultDir}`
      })
    } finally {
      setIsSelectingDirectory(false)
    }
  }

  const handleNext = () => {
    if (step === 1) {
      if (!username.trim()) {
        addNotification({
          type: 'warning',
          title: '请输入用户名',
          message: '用户名不能为空'
        })
        return
      }
      setStep(2)
    } else if (step === 2) {
      if (!workspaceDirectory) {
        addNotification({
          type: 'warning',
          title: '请选择工作目录',
          message: '需要选择一个目录来存储您的文件'
        })
        return
      }
      handleComplete()
    }
  }

  const handleComplete = async () => {
    try {
      // 更新用户设置
      updateSettings({
        username: username.trim(),
        workspaceDirectory
      })

      // 检查是否在Electron环境中
      if (window.electronAPI?.fileSystem?.reinitialize) {
        // Electron环境：重新初始化文件系统
        const result = await fileSystemApi.reinitialize(workspaceDirectory)
        if (!result.success) {
          throw new Error(result.error || '文件系统初始化失败')
        }
      } else {
        // 浏览器环境：模拟成功
        console.log('Browser environment: Skipping file system initialization')
      }

      completeFirstTimeSetup()

      addNotification({
        type: 'success',
        title: '设置完成',
        message: `欢迎使用 PaoLife，${username}！`
      })

      onComplete()
    } catch (error) {
      console.error('Setup error:', error)
      addNotification({
        type: 'error',
        title: '设置失败',
        message: error instanceof Error ? error.message : '无法初始化工作目录，请重试'
      })
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>欢迎使用 PaoLife</DialogTitle>
          <DialogDescription>让我们进行一些基本设置来开始使用</DialogDescription>
        </DialogHeader>

        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">步骤 1: 设置用户名</CardTitle>
              <CardDescription>请输入您的用户名，这将用于个性化您的体验</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">用户名</label>
                <Input
                  placeholder="请输入您的用户名"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleNext()}
                  autoFocus
                />
              </div>
              <div className="flex justify-end">
                <Button onClick={handleNext} disabled={!username.trim()}>
                  下一步
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {step === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">步骤 2: 选择工作目录</CardTitle>
              <CardDescription>选择一个目录来存储您的笔记和文件</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">工作目录</label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      placeholder="输入目录路径或点击浏览..."
                      value={workspaceDirectory}
                      onChange={(e) => setWorkspaceDirectory(e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      variant="outline"
                      onClick={handleSelectDirectory}
                      disabled={isSelectingDirectory}
                    >
                      {isSelectingDirectory ? '选择中...' : '浏览'}
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setWorkspaceDirectory('./PaoLife-Workspace')}
                    >
                      使用默认目录
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => setWorkspaceDirectory('')}>
                      清空
                    </Button>
                  </div>
                </div>
                {workspaceDirectory && (
                  <p className="text-xs text-muted-foreground mt-1">已选择: {workspaceDirectory}</p>
                )}
              </div>
              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  上一步
                </Button>
                <Button onClick={handleNext} disabled={!workspaceDirectory}>
                  完成设置
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </DialogContent>
    </Dialog>
  )
}
