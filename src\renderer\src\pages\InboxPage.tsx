import { useState, useEffect, useMemo } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, PageHeaderActions, EmptyStates } from '../components/shared'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { But<PERSON> } from '../components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '../components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { Input } from '../components/ui/input'
import InboxItem, { type InboxItem as InboxItemType } from '../components/features/InboxItem'
import QuickCaptureDialog from '../components/features/QuickCaptureDialog'
import TaskList from '../components/features/TaskList'
import CreateTaskDialog from '../components/features/CreateTaskDialog'
import { useLanguage } from '../contexts/LanguageContext'
import { useTaskStore } from '../store/taskStore'
import type { ExtendedTask } from '../store/taskStore'

export function InboxPage() {
  const [items, setItems] = useState<InboxItemType[]>([])
  const [isQuickCaptureOpen, setIsQuickCaptureOpen] = useState(false)
  const [isCreateTaskDialogOpen, setIsCreateTaskDialogOpen] = useState(false)
  const [filterType, setFilterType] = useState<string>('all')
  const [filterProcessed, setFilterProcessed] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const { t } = useLanguage()

  // Task store for unassigned tasks
  const { tasks, addTask, updateTask, deleteTask, moveTask } = useTaskStore()

  // Get unassigned tasks (no project and no area)
  const unassignedTasks = useMemo(() => {
    return tasks.filter((task) => !task.projectId && !task.areaId)
  }, [tasks])

  // Mock data - in real implementation, this would come from a database
  useEffect(() => {
    const mockItems: InboxItemType[] = [
      {
        id: '1',
        content: 'Research new project management tools for the team',
        type: 'task',
        priority: 'high',
        tags: ['work', 'research'],
        processed: false,
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '2',
        content: 'Interesting article about productivity: https://example.com/productivity-tips',
        type: 'link',
        priority: 'medium',
        tags: ['productivity', 'reading'],
        processed: true,
        processedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        processedTo: { type: 'resource', id: 'res1', name: 'Productivity Resources' },
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '3',
        content: 'Idea for a mobile app that helps track daily habits with gamification',
        type: 'idea',
        priority: 'medium',
        tags: ['app', 'habits', 'gamification'],
        processed: false,
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '4',
        content: 'Meeting notes from client call - need to follow up on requirements',
        type: 'note',
        priority: 'high',
        tags: ['meeting', 'client', 'followup'],
        processed: false,
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '5',
        content: 'Buy groceries: milk, bread, eggs, vegetables',
        type: 'task',
        priority: 'low',
        tags: ['personal', 'shopping'],
        processed: true,
        processedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        processedTo: { type: 'area', id: 'area1', name: 'Personal Life' },
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      }
    ]

    setTimeout(() => {
      setItems(mockItems)
      setIsLoading(false)
    }, 500)
  }, [])

  const handleCreateItem = (itemData: Omit<InboxItemType, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newItem: InboxItemType = {
      ...itemData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    setItems((prev) => [newItem, ...prev])
  }

  const handleEditItem = (updatedItem: InboxItemType) => {
    setItems((prev) => prev.map((item) => (item.id === updatedItem.id ? updatedItem : item)))
  }

  const handleDeleteItem = (itemId: string) => {
    if (confirm('Are you sure you want to delete this item?')) {
      setItems((prev) => prev.filter((item) => item.id !== itemId))
    }
  }

  const handleProcessItem = (
    itemId: string,
    destination: { type: 'project' | 'area' | 'resource' | 'archive'; id: string; name: string }
  ) => {
    setItems((prev) =>
      prev.map((item) =>
        item.id === itemId
          ? {
              ...item,
              processed: true,
              processedAt: new Date().toISOString(),
              processedTo: destination,
              updatedAt: new Date().toISOString()
            }
          : item
      )
    )
  }

  const handleToggleProcessed = (itemId: string) => {
    setItems((prev) =>
      prev.map((item) =>
        item.id === itemId
          ? {
              ...item,
              processed: !item.processed,
              processedAt: item.processed ? undefined : new Date().toISOString(),
              processedTo: item.processed ? undefined : item.processedTo,
              updatedAt: new Date().toISOString()
            }
          : item
      )
    )
  }

  // Task handlers
  const handleCreateTask = async (
    taskData: Omit<ExtendedTask, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    const newTask: ExtendedTask = {
      ...taskData,
      id: `task-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    addTask(newTask)
  }

  const handleToggleTask = (taskId: string, completed: boolean) => {
    updateTask(taskId, { completed })
  }

  const handleEditTask = (task: ExtendedTask) => {
    // TODO: Implement task editing
    console.log('Edit task:', task)
  }

  const handleDeleteTask = (taskId: string) => {
    deleteTask(taskId)
  }

  const handleTaskMove = (taskId: string, newParentId?: string, newIndex?: number) => {
    moveTask(taskId, newParentId, newIndex)
  }

  // Filter items
  const filteredItems = items.filter((item) => {
    const matchesType = filterType === 'all' || item.type === filterType
    const matchesProcessed =
      filterProcessed === 'all' ||
      (filterProcessed === 'processed' && item.processed) ||
      (filterProcessed === 'unprocessed' && !item.processed)
    const matchesSearch =
      !searchQuery ||
      item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    return matchesType && matchesProcessed && matchesSearch
  })

  const unprocessedCount = items.filter((item) => !item.processed).length

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader
        title={t('pages.inbox.title')}
        description={t('pages.inbox.description')}
        badge={{
          text:
            unprocessedCount > 0
              ? `${unprocessedCount} ${t('pages.inbox.unprocessed')}`
              : t('pages.inbox.allProcessed'),
          variant: unprocessedCount > 0 ? 'secondary' : 'default'
        }}
        actions={
          <PageHeaderActions.Create onClick={() => setIsQuickCaptureOpen(true)}>
            {t('pages.inbox.quickCapture')}
          </PageHeaderActions.Create>
        }
      />

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder={t('pages.inbox.searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <div className="flex gap-2">
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('pages.inbox.filters.type.allTypes')}</SelectItem>
              <SelectItem value="note">📝 {t('pages.inbox.filters.type.notes')}</SelectItem>
              <SelectItem value="task">✅ {t('pages.inbox.filters.type.tasks')}</SelectItem>
              <SelectItem value="idea">💡 {t('pages.inbox.filters.type.ideas')}</SelectItem>
              <SelectItem value="link">🔗 {t('pages.inbox.filters.type.links')}</SelectItem>
              <SelectItem value="file">📄 {t('pages.inbox.filters.type.files')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterProcessed} onValueChange={setFilterProcessed}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('pages.inbox.filters.status.allItems')}</SelectItem>
              <SelectItem value="unprocessed">
                {t('pages.inbox.filters.status.unprocessed')}
              </SelectItem>
              <SelectItem value="processed">{t('pages.inbox.filters.status.processed')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Tabs for Inbox Items and Unassigned Tasks */}
      <Tabs defaultValue="inbox" className="space-y-4">
        <TabsList>
          <TabsTrigger value="inbox">收件箱项目 ({filteredItems.length})</TabsTrigger>
          <TabsTrigger value="tasks">未分配任务 ({unassignedTasks.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="inbox">
          <Card>
            <CardHeader>
              <CardTitle>收件箱项目</CardTitle>
              <CardDescription>需要处理和分类的项目</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    <span>Loading inbox...</span>
                  </div>
                </div>
              ) : filteredItems.length === 0 ? (
                <div className="max-w-md mx-auto mt-12">
                  {items.length === 0 ? (
                    <EmptyStates.Tasks onCreate={() => setIsQuickCaptureOpen(true)} />
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <div className="text-4xl mb-2">🔍</div>
                      <p className="text-sm">No items match your filters</p>
                      <p className="text-xs mt-1">Try adjusting your search or filters</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredItems.map((item) => (
                    <InboxItem
                      key={item.id}
                      item={item}
                      onEdit={handleEditItem}
                      onDelete={handleDeleteItem}
                      onProcess={handleProcessItem}
                      onToggleProcessed={handleToggleProcessed}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>未分配任务</CardTitle>
                  <CardDescription>没有关联项目或领域的任务</CardDescription>
                </div>
                <Button onClick={() => setIsCreateTaskDialogOpen(true)}>
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  创建任务
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <TaskList
                tasks={unassignedTasks}
                onTaskToggle={handleToggleTask}
                onTaskEdit={handleEditTask}
                onTaskDelete={handleDeleteTask}
                onTaskAddSubtask={() => {
                  // TODO: Implement add subtask
                }}
                onTaskMove={handleTaskMove}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Capture Dialog */}
      <QuickCaptureDialog
        isOpen={isQuickCaptureOpen}
        onClose={() => setIsQuickCaptureOpen(false)}
        onSubmit={handleCreateItem}
      />

      {/* Create Task Dialog */}
      <CreateTaskDialog
        isOpen={isCreateTaskDialogOpen}
        onClose={() => setIsCreateTaskDialogOpen(false)}
        onSubmit={handleCreateTask}
      />
    </div>
  )
}

export default InboxPage
