import { useState, useCallback, memo, useMemo } from 'react'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import {
  Clock,
  Calendar,
  Settings,
  Play,
  Pause,
  CheckCircle,
  AlertCircle,
  Timer,
  ChevronRight,
  ChevronDown,
  Plus,
  MoreHorizontal
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { useTaskStore } from '../../store/taskStore'
import type { Task } from '../../../../shared/types'
import type { ExtendedTask } from '../../store/taskStore'

interface HierarchicalTaskItemProps {
  task: ExtendedTask & { children?: ExtendedTask[] }
  level?: number
  maxLevel?: number // 最大显示层级，超过则折叠
  onToggleComplete: (task: Task) => void
  onUpdateStatus: (task: Task, status: string) => void
  onOpenAttributes: (task: Task) => void
  onDelete?: (task: Task) => void
  onAddSubtask?: (parentId: string) => void
  onTaskClick?: (task: ExtendedTask) => void // 新增：点击任务时的回调
  expandedTasks?: Set<string> // 展开的任务ID集合
  onToggleExpand?: (taskId: string) => void // 切换展开状态
  showHierarchy?: boolean
}

export function HierarchicalTaskItem({
  task,
  level = 0,
  maxLevel = 10,
  onToggleComplete,
  onUpdateStatus,
  onOpenAttributes,
  onDelete,
  onAddSubtask,
  onTaskClick,
  expandedTasks = new Set(),
  onToggleExpand,
  showHierarchy = true
}: HierarchicalTaskItemProps) {
  // 使用 useMemo 优化计算
  const taskInfo = useMemo(() => {
    const hasChildren = task.children && task.children.length > 0
    const isExpanded = expandedTasks.has(task.id)
    const shouldShowChildren = hasChildren && isExpanded && level < maxLevel
    const isOverdue = task.deadline && new Date(task.deadline) < new Date() && !task.completed

    return {
      hasChildren,
      isExpanded,
      shouldShowChildren,
      isOverdue
    }
  }, [task.children, task.deadline, task.completed, expandedTasks, task.id, level, maxLevel])

  const { hasChildren, isExpanded, shouldShowChildren, isOverdue } = taskInfo

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200'
    }
  }

  const shouldShowPriority = (priority?: string) => {
    return priority && priority !== 'none' && priority !== ''
  }

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'in_progress':
        return <Play className="h-3 w-3" />
      case 'blocked':
        return <AlertCircle className="h-3 w-3" />
      case 'review':
        return <Clock className="h-3 w-3" />
      case 'done':
        return <CheckCircle className="h-3 w-3" />
      default:
        return <Timer className="h-3 w-3" />
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'blocked':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'review':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'done':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200'
    }
  }

  const formatDate = (date: string | Date | null) => {
    if (!date) return null
    return new Date(date).toLocaleDateString()
  }

  const handleToggleExpand = useCallback(() => {
    if (hasChildren && onToggleExpand) {
      onToggleExpand(task.id)
    }
  }, [hasChildren, onToggleExpand, task.id])

  const handleTaskClick = useCallback((e: React.MouseEvent) => {
    // 避免在点击其他交互元素时触发
    if (
      e.target instanceof HTMLElement &&
      (e.target.closest('input') ||
        e.target.closest('button') ||
        e.target.closest('[role="button"]'))
    ) {
      return
    }
    onTaskClick?.(task)
  }, [onTaskClick, task])

  // 计算缩进样式 - 响应式缩进
  const indentStyle = {
    paddingLeft: `${level * (window.innerWidth < 640 ? 16 : 24) + (window.innerWidth < 640 ? 12 : 16)}px`
  }

  // 层级连接线样式
  const renderHierarchyLines = () => {
    if (!showHierarchy || level === 0) return null

    const lines = []
    for (let i = 0; i < level; i++) {
      lines.push(
        <div
          key={i}
          className="absolute w-px bg-gray-200"
          style={{
            left: `${i * 24 + 28}px`,
            top: 0,
            bottom: 0
          }}
        />
      )
    }

    // 水平连接线
    lines.push(
      <div
        key="horizontal"
        className="absolute h-px bg-gray-200"
        style={{
          left: `${(level - 1) * 24 + 28}px`,
          top: '24px',
          width: '16px'
        }}
      />
    )

    return <div className="absolute inset-0 pointer-events-none">{lines}</div>
  }

  return (
    <div className="relative">
      {/* 层级连接线 */}
      {renderHierarchyLines()}

      {/* 任务卡片 */}
      <div
        className={cn(
          'group relative border rounded-lg hover:bg-accent/50 transition-all duration-200 cursor-pointer',
          task.completed && 'opacity-60',
          isOverdue && 'border-red-200 bg-red-50',
          'hover:shadow-sm'
        )}
        style={indentStyle}
        onClick={handleTaskClick}
      >
        <div className="p-4">
          <div className="flex items-start gap-3 w-full">
            {/* 展开/折叠按钮 */}
            {hasChildren && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-accent"
                onClick={(e) => {
                  e.stopPropagation()
                  handleToggleExpand()
                }}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            )}

            {/* 复选框 */}
            <input
              type="checkbox"
              checked={task.completed}
              onChange={(e) => {
                e.stopPropagation()
                onToggleComplete(task as Task)
              }}
              className="mt-1 cursor-pointer"
              aria-label={`Mark task "${task.content}" as ${task.completed ? 'incomplete' : 'complete'}`}
            />

            {/* 主要内容 */}
            <div className="flex-1 min-w-0">
              {/* 任务标题和基本信息 */}
              <div className="flex items-center gap-2 mb-2">
                <h4 className={cn(
                  "font-medium text-sm",
                  task.completed && "line-through text-muted-foreground"
                )}>
                  {task.content}
                </h4>

                {/* 子任务数量指示器 */}
                {hasChildren && (
                  <Badge variant="secondary" className="text-xs">
                    {task.children!.length} subtask{task.children!.length !== 1 ? 's' : ''}
                  </Badge>
                )}

                {/* 优先级标签 */}
                {shouldShowPriority(task.priority) && (
                  <Badge variant="outline" className={cn("text-xs", getPriorityColor(task.priority))}>
                    {task.priority}
                  </Badge>
                )}

                {/* 状态标签 */}
                {task.status && task.status !== 'todo' && (
                  <Badge variant="outline" className={cn("text-xs", getStatusColor(task.status))}>
                    {getStatusIcon(task.status)}
                    <span className="ml-1 capitalize">{task.status.replace('_', ' ')}</span>
                  </Badge>
                )}

                {/* 逾期警告 */}
                {isOverdue && (
                  <Badge variant="destructive" className="text-xs">
                    Overdue
                  </Badge>
                )}
              </div>

              {/* 描述 */}
              {task.description && (
                <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                  {task.description}
                </p>
              )}

              {/* 子任务进度条 */}
              <HierarchicalSubtaskProgress task={task} />

              {/* 时间信息 */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                {task.deadline && (
                  <span className={cn('flex items-center gap-1', isOverdue && 'text-red-600')}>
                    <Calendar className="w-3 h-3" />
                    Due: {formatDate(task.deadline)}
                  </span>
                )}
                {task.estimatedHours && (
                  <span className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    Est: {task.estimatedHours}h
                  </span>
                )}
              </div>
            </div>

            {/* 快速操作按钮 - 仅在悬停时显示 */}
            <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-1">
              {onAddSubtask && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={(e) => {
                    e.stopPropagation()
                    onAddSubtask(task.id)
                  }}
                  title="Add subtask"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 子任务递归渲染 */}
      {shouldShowChildren && (
        <div className="mt-2 space-y-2">
          {task.children!.map((child) => (
            <HierarchicalTaskItem
              key={child.id}
              task={child as ExtendedTask & { children?: ExtendedTask[] }}
              level={level + 1}
              maxLevel={maxLevel}
              onToggleComplete={onToggleComplete}
              onUpdateStatus={onUpdateStatus}
              onOpenAttributes={onOpenAttributes}
              onDelete={onDelete}
              onAddSubtask={onAddSubtask}
              onTaskClick={onTaskClick}
              expandedTasks={expandedTasks}
              onToggleExpand={onToggleExpand}
              showHierarchy={showHierarchy}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// 层级任务的子任务进度组件
function HierarchicalSubtaskProgress({ task }: { task: Task }) {
  const { getSubtaskProgress } = useTaskStore()
  const progress = getSubtaskProgress(task.id)

  // 只有当任务有子任务时才显示进度条
  if (progress.total === 1) {
    return null
  }

  return (
    <div className="mb-2">
      <div className="flex items-center gap-2 mb-1">
        <span className="text-xs text-muted-foreground">子任务</span>
        <span className="text-xs font-medium">
          {progress.completed}/{progress.total} ({progress.percentage}%)
        </span>
      </div>
      <Progress value={progress.percentage} className="h-1" />
    </div>
  )
}

export default memo(HierarchicalTaskItem)
