import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'

interface CreateDeliverableDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
}

export function CreateDeliverableDialog({
  isOpen,
  onClose,
  onSubmit
}: CreateDeliverableDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'document',
    status: 'planned',
    priority: 'medium',
    plannedDate: '',
    deadline: '',
    content: ''
  })

  const deliverableTypes = [
    { value: 'document', label: '📄 Document', description: 'Written deliverable like reports, specifications' },
    { value: 'milestone', label: '🎯 Milestone', description: 'Important project checkpoint or achievement' },
    { value: 'metric', label: '📊 Metric', description: 'Quantifiable outcome or KPI target' },
    { value: 'artifact', label: '🔧 Artifact', description: 'Tangible output like code, design, prototype' }
  ]

  const statusOptions = [
    { value: 'planned', label: 'Planned', description: 'Not yet started' },
    { value: 'in_progress', label: 'In Progress', description: 'Currently being worked on' },
    { value: 'completed', label: 'Completed', description: 'Work finished, ready for delivery' },
    { value: 'delivered', label: 'Delivered', description: 'Handed over to stakeholders' },
    { value: 'accepted', label: 'Accepted', description: 'Approved and accepted by stakeholders' }
  ]

  const priorityOptions = [
    { value: 'low', label: 'Low', color: 'text-green-600' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600' },
    { value: 'high', label: 'High', color: 'text-orange-600' },
    { value: 'critical', label: 'Critical', color: 'text-red-600' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.title.trim()) return

    setIsSubmitting(true)
    try {
      const deliverableData = {
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        type: formData.type,
        status: formData.status,
        priority: formData.priority,
        plannedDate: formData.plannedDate ? new Date(formData.plannedDate) : undefined,
        deadline: formData.deadline ? new Date(formData.deadline) : undefined,
        content: formData.content.trim() || undefined
      }

      await onSubmit(deliverableData)
      onClose()

      // Reset form
      setFormData({
        title: '',
        description: '',
        type: 'document',
        status: 'planned',
        priority: 'medium',
        plannedDate: '',
        deadline: '',
        content: ''
      })
    } catch (error) {
      console.error('Failed to create deliverable:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Deliverable</DialogTitle>
          <DialogDescription>
            Define a project outcome or deliverable to track progress and completion.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
              placeholder="e.g., User Manual Documentation, MVP Release, Performance Metrics"
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
              placeholder="Detailed description of what needs to be delivered..."
              rows={3}
            />
          </div>

          {/* Type and Status */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Type</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, type: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="max-w-xs">
                  {deliverableTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="max-w-xs">
                        <div className="font-medium truncate">{type.label}</div>
                        <div className="text-xs text-muted-foreground line-clamp-2">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="max-w-xs">
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      <div className="max-w-xs">
                        <div className="font-medium truncate">{status.label}</div>
                        <div className="text-xs text-muted-foreground line-clamp-2">{status.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, priority: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {priorityOptions.map((priority) => (
                  <SelectItem key={priority.value} value={priority.value}>
                    <span className={priority.color}>{priority.label}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="plannedDate">Planned Date</Label>
              <Input
                id="plannedDate"
                type="date"
                value={formData.plannedDate}
                onChange={(e) => setFormData((prev) => ({ ...prev, plannedDate: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="deadline">Deadline</Label>
              <Input
                id="deadline"
                type="date"
                value={formData.deadline}
                onChange={(e) => setFormData((prev) => ({ ...prev, deadline: e.target.value }))}
              />
            </div>
          </div>

          {/* Content */}
          <div className="space-y-2">
            <Label htmlFor="content">Additional Details</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
              placeholder="Acceptance criteria, requirements, notes..."
              rows={4}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={!formData.title.trim() || isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Deliverable'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateDeliverableDialog
