import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { Task, Tag, TaskTag, Checklist, ChecklistInstance } from '../../../shared/types'

// Extended Task type for UI purposes
export interface ExtendedTask extends Task {
  status?: string
  title?: string
}

// Extended TaskTag type for UI purposes
export interface ExtendedTaskTag extends Omit<TaskTag, 'id'> {
  id?: string
}

// Extended ChecklistInstance type for UI purposes
export interface ExtendedChecklistInstance extends ChecklistInstance {
  completedItems?: number[]
}

export interface TaskState {
  tasks: ExtendedTask[]
  tags: Tag[]
  taskTags: ExtendedTaskTag[]
  checklists: Checklist[]
  checklistInstances: ExtendedChecklistInstance[]
  currentTask: ExtendedTask | null
  selectedTasks: string[]
  filter: {
    status?: string
    priority?: string
    projectId?: string
    areaId?: string
    tagIds?: string[]
    search?: string
  }
  loading: boolean
  error: string | null
}

export interface TaskActions {
  // Task CRUD operations
  setTasks: (tasks: ExtendedTask[]) => void
  addTask: (task: ExtendedTask) => void
  updateTask: (id: string, updates: Partial<ExtendedTask>) => void
  deleteTask: (id: string) => void
  completeTask: (id: string) => void

  // Task hierarchy and movement
  moveTask: (taskId: string, newParentId?: string, newIndex?: number) => void
  updateTaskHierarchy: (taskId: string, newParentId?: string) => void
  getTaskChildren: (taskId: string) => ExtendedTask[]
  getTaskTree: (projectId?: string) => ExtendedTask[]
  getDescendantCount: (taskId: string) => number
  isCircularDependency: (parentId: string, childId: string) => boolean
  getSubtaskProgress: (taskId: string) => { completed: number; total: number; percentage: number }

  // Task selection
  setCurrentTask: (task: ExtendedTask | null) => void
  selectTask: (id: string) => void
  deselectTask: (id: string) => void
  selectAllTasks: () => void
  clearSelection: () => void

  // Tag management
  setTags: (tags: Tag[]) => void
  addTag: (tag: Tag) => void
  updateTag: (id: string, updates: Partial<Tag>) => void
  deleteTag: (id: string) => void

  // Task-Tag relationships
  setTaskTags: (taskTags: ExtendedTaskTag[]) => void
  addTagToTask: (taskId: string, tagId: string) => void
  removeTagFromTask: (taskId: string, tagId: string) => void

  // Checklist management
  setChecklists: (checklists: Checklist[]) => void
  addChecklist: (checklist: Checklist) => void
  updateChecklist: (id: string, updates: Partial<Checklist>) => void
  deleteChecklist: (id: string) => void

  // Checklist instance management
  setChecklistInstances: (instances: ExtendedChecklistInstance[]) => void
  addChecklistInstance: (instance: ExtendedChecklistInstance) => void
  updateChecklistInstance: (id: string, updates: Partial<ExtendedChecklistInstance>) => void
  toggleChecklistItem: (instanceId: string, itemIndex: number) => void

  // Filtering and search
  setFilter: (filter: Partial<TaskState['filter']>) => void
  clearFilter: () => void

  // Loading and error states
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // Computed getters
  getFilteredTasks: () => ExtendedTask[]
  getTasksByProject: (projectId: string) => ExtendedTask[]
  getTasksByArea: (areaId: string) => ExtendedTask[]
  getTasksByTag: (tagId: string) => ExtendedTask[]

  // Async operations
  fetchTasks: () => Promise<void>
  createTask: (data: Omit<ExtendedTask, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  fetchTags: () => Promise<void>
}

export type TaskStore = TaskState & TaskActions

export const useTaskStore = create<TaskStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        tasks: [],
        tags: [],
        taskTags: [],
        checklists: [],
        checklistInstances: [],
        currentTask: null,
        selectedTasks: [],
        filter: {},
        loading: false,
        error: null,

        // Task actions
        setTasks: (tasks) => set({ tasks }),

        addTask: (task) =>
          set((state) => ({
            tasks: [task, ...state.tasks]
          })),

        updateTask: (id, updates) =>
          set((state) => ({
            tasks: state.tasks.map((task) => (task.id === id ? { ...task, ...updates } : task)),
            currentTask:
              state.currentTask?.id === id
                ? { ...state.currentTask, ...updates }
                : state.currentTask
          })),

        deleteTask: (id) => {
          const state = get()

          // Get all descendant task IDs (children, grandchildren, etc.)
          const getDescendantIds = (parentId: string): string[] => {
            const children = state.tasks.filter((task) => task.parentId === parentId)
            const allDescendants = [...children.map((child) => child.id)]

            // Recursively get descendants of children
            children.forEach((child) => {
              allDescendants.push(...getDescendantIds(child.id))
            })

            return allDescendants
          }

          const descendantIds = getDescendantIds(id)
          const allIdsToDelete = [id, ...descendantIds]

          console.log(
            `🗑️ Deleting task ${id} and ${descendantIds.length} descendants:`,
            allIdsToDelete
          )

          set((state) => ({
            tasks: state.tasks.filter((task) => !allIdsToDelete.includes(task.id)),
            currentTask: allIdsToDelete.includes(state.currentTask?.id || '')
              ? null
              : state.currentTask,
            selectedTasks: state.selectedTasks.filter((taskId) => !allIdsToDelete.includes(taskId))
          }))
        },

        completeTask: (id) =>
          get().updateTask(id, {
            completed: true
          }),

        // Task hierarchy and movement operations
        moveTask: (taskId, newParentId, newIndex) => {
          const state = get()

          try {
            // Validate input parameters
            if (!taskId || typeof taskId !== 'string') {
              console.error('Invalid taskId provided to moveTask:', taskId)
              return
            }

            // Check if task exists
            const task = state.tasks.find((t) => t.id === taskId)
            if (!task) {
              console.error('Task not found:', taskId)
              return
            }

            // Validate newParentId if provided
            if (newParentId && !state.tasks.find((t) => t.id === newParentId)) {
              console.error('Parent task not found:', newParentId)
              return
            }

            // Prevent circular dependency
            if (newParentId && state.isCircularDependency(taskId, newParentId)) {
              console.warn('Cannot move task: would create circular dependency')
              return
            }

            // Prevent moving task to itself
            if (taskId === newParentId) {
              console.warn('Cannot move task to itself')
              return
            }

            console.log(
              `🔄 Moving task ${taskId} to parent ${newParentId || 'root'} at index ${newIndex}`
            )

            // Update the task's parent
            state.updateTask(taskId, { parentId: newParentId || null })

            // Handle reordering if newIndex is provided
            if (typeof newIndex === 'number') {
              set((currentState) => {
                const newTasks = [...currentState.tasks]

                // Find the task to move
                const taskIndex = newTasks.findIndex((t) => t.id === taskId)
                if (taskIndex === -1) return currentState

                // Remove the task from its current position
                const [movedTask] = newTasks.splice(taskIndex, 1)

                // Find siblings with the same parent
                const siblings = newTasks.filter((t) => t.parentId === newParentId)
                const targetSibling = siblings[newIndex]

                if (targetSibling) {
                  // Find the target position in the full array
                  const targetIndex = newTasks.findIndex((t) => t.id === targetSibling.id)
                  // Insert at the target position
                  newTasks.splice(targetIndex, 0, movedTask)
                } else {
                  // Add at the end if no target sibling
                  newTasks.push(movedTask)
                }

                return { ...currentState, tasks: newTasks }
              })
            }

            console.log(`✅ Task moved successfully`)
          } catch (error) {
            console.error('Error in moveTask:', error)
            state.setError('Failed to move task')
          }
        },

        updateTaskHierarchy: (taskId, newParentId) => {
          const state = get()

          // Prevent circular dependency
          if (newParentId && state.isCircularDependency(taskId, newParentId)) {
            console.warn('Cannot update hierarchy: would create circular dependency')
            return
          }

          state.updateTask(taskId, { parentId: newParentId || null })
        },

        getTaskChildren: (taskId) => {
          const state = get()
          return state.tasks.filter((task) => task.parentId === taskId)
        },

        getTaskTree: (projectId) => {
          const state = get()
          let tasks = projectId
            ? state.tasks.filter((task) => task.projectId === projectId)
            : state.tasks

          // Build tree structure
          const taskMap = new Map<string, ExtendedTask & { children: ExtendedTask[] }>()
          const rootTasks: (ExtendedTask & { children: ExtendedTask[] })[] = []

          // Initialize all tasks with children array
          tasks.forEach((task) => {
            taskMap.set(task.id, { ...task, children: [] })
          })

          // Build tree structure
          tasks.forEach((task) => {
            const taskWithChildren = taskMap.get(task.id)!
            if (task.parentId && taskMap.has(task.parentId)) {
              taskMap.get(task.parentId)!.children.push(taskWithChildren)
            } else {
              rootTasks.push(taskWithChildren)
            }
          })

          return rootTasks
        },

        isCircularDependency: (parentId, childId) => {
          const state = get()

          // Prevent infinite recursion with depth limit
          const checkCircular = (
            currentParentId: string,
            currentChildId: string,
            depth = 0
          ): boolean => {
            // Prevent infinite loops with reasonable depth limit
            if (depth > 50) {
              console.warn('Maximum depth reached in circular dependency check')
              return true // Assume circular to be safe
            }

            const checkTask = state.tasks.find((task) => task.id === currentChildId)
            if (!checkTask) return false
            if (checkTask.parentId === currentParentId) return true
            if (checkTask.parentId) {
              return checkCircular(currentParentId, checkTask.parentId, depth + 1)
            }
            return false
          }

          try {
            return checkCircular(parentId, childId)
          } catch (error) {
            console.error('Error in circular dependency check:', error)
            return true // Assume circular to be safe
          }
        },

        getDescendantCount: (taskId) => {
          const state = get()

          const countDescendants = (parentId: string): number => {
            const children = state.tasks.filter((task) => task.parentId === parentId)
            let count = children.length

            // Recursively count descendants of children
            children.forEach((child) => {
              count += countDescendants(child.id)
            })

            return count
          }

          return countDescendants(taskId)
        },

        getSubtaskProgress: (taskId) => {
          const state = get()

          // 获取直接子任务（不包括孙子任务）
          const directChildren = state.tasks.filter((task) => task.parentId === taskId)

          if (directChildren.length === 0) {
            // 如果没有子任务，返回基于自身完成状态的进度
            const task = state.tasks.find(t => t.id === taskId)
            return {
              completed: task?.completed ? 1 : 0,
              total: 1,
              percentage: task?.completed ? 100 : 0
            }
          }

          // 计算已完成的子任务数量
          const completedChildren = directChildren.filter((task) => task.completed)
          const percentage = Math.round((completedChildren.length / directChildren.length) * 100)

          return {
            completed: completedChildren.length,
            total: directChildren.length,
            percentage
          }
        },

        // Selection actions
        setCurrentTask: (task) => set({ currentTask: task }),

        selectTask: (id) =>
          set((state) => ({
            selectedTasks: state.selectedTasks.includes(id)
              ? state.selectedTasks
              : [...state.selectedTasks, id]
          })),

        deselectTask: (id) =>
          set((state) => ({
            selectedTasks: state.selectedTasks.filter((taskId) => taskId !== id)
          })),

        selectAllTasks: () => {
          const filteredTasks = get().getFilteredTasks()
          set({ selectedTasks: filteredTasks.map((task) => task.id) })
        },

        clearSelection: () => set({ selectedTasks: [] }),

        // Tag actions
        setTags: (tags) => set({ tags }),

        addTag: (tag) =>
          set((state) => ({
            tags: [tag, ...state.tags]
          })),

        updateTag: (id, updates) =>
          set((state) => ({
            tags: state.tags.map((tag) => (tag.id === id ? { ...tag, ...updates } : tag))
          })),

        deleteTag: (id) =>
          set((state) => ({
            tags: state.tags.filter((tag) => tag.id !== id),
            taskTags: state.taskTags.filter((taskTag) => taskTag.tagId !== id)
          })),

        // Task-Tag relationship actions
        setTaskTags: (taskTags) => set({ taskTags }),

        addTagToTask: (taskId, tagId) => {
          const exists = get().taskTags.some((tt) => tt.taskId === taskId && tt.tagId === tagId)
          if (!exists) {
            const newTaskTag: ExtendedTaskTag = {
              taskId,
              tagId
            }
            set((state) => ({
              taskTags: [...state.taskTags, newTaskTag]
            }))
          }
        },

        removeTagFromTask: (taskId, tagId) =>
          set((state) => ({
            taskTags: state.taskTags.filter((tt) => !(tt.taskId === taskId && tt.tagId === tagId))
          })),

        // Checklist actions
        setChecklists: (checklists) => set({ checklists }),
        addChecklist: (checklist) =>
          set((state) => ({
            checklists: [checklist, ...state.checklists]
          })),

        updateChecklist: (id, updates) =>
          set((state) => ({
            checklists: state.checklists.map((checklist) =>
              checklist.id === id ? { ...checklist, ...updates } : checklist
            )
          })),

        deleteChecklist: (id) =>
          set((state) => ({
            checklists: state.checklists.filter((checklist) => checklist.id !== id)
          })),

        // Checklist instance actions
        setChecklistInstances: (instances) => set({ checklistInstances: instances }),

        addChecklistInstance: (instance) =>
          set((state) => ({
            checklistInstances: [instance, ...state.checklistInstances]
          })),

        updateChecklistInstance: (id, updates) =>
          set((state) => ({
            checklistInstances: state.checklistInstances.map((instance) =>
              instance.id === id ? { ...instance, ...updates } : instance
            )
          })),

        toggleChecklistItem: (instanceId, itemIndex) => {
          const instance = get().checklistInstances.find((i) => i.id === instanceId)
          if (instance && instance.completedItems) {
            const completedItems = [...instance.completedItems]
            const isCompleted = completedItems.includes(itemIndex)

            if (isCompleted) {
              completedItems.splice(completedItems.indexOf(itemIndex), 1)
            } else {
              completedItems.push(itemIndex)
            }

            get().updateChecklistInstance(instanceId, { completedItems })
          }
        },

        // Filter actions
        setFilter: (filter) =>
          set((state) => ({
            filter: { ...state.filter, ...filter }
          })),

        clearFilter: () => set({ filter: {} }),

        // Common actions
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),

        // Computed getters
        getFilteredTasks: () => {
          const { tasks, filter } = get()
          let filtered = tasks

          if (filter.status) {
            filtered = filtered.filter((task) => task.status === filter.status)
          }

          if (filter.priority) {
            filtered = filtered.filter((task) => task.priority === filter.priority)
          }

          if (filter.projectId) {
            filtered = filtered.filter((task) => task.projectId === filter.projectId)
          }

          if (filter.areaId) {
            filtered = filtered.filter((task) => task.areaId === filter.areaId)
          }

          if (filter.search) {
            const searchLower = filter.search.toLowerCase()
            filtered = filtered.filter(
              (task) =>
                (task.title && task.title.toLowerCase().includes(searchLower)) ||
                (task.description && task.description.toLowerCase().includes(searchLower))
            )
          }

          if (filter.tagIds && filter.tagIds.length > 0) {
            const { taskTags } = get()
            filtered = filtered.filter((task) =>
              filter.tagIds!.some((tagId) =>
                taskTags.some((tt) => tt.taskId === task.id && tt.tagId === tagId)
              )
            )
          }

          return filtered
        },

        getTasksByProject: (projectId) =>
          get().tasks.filter((task) => task.projectId === projectId),

        getTasksByArea: (areaId) => get().tasks.filter((task) => task.areaId === areaId),

        getTasksByTag: (tagId) => {
          const { tasks, taskTags } = get()
          const taskIds = taskTags.filter((tt) => tt.tagId === tagId).map((tt) => tt.taskId)
          return tasks.filter((task) => taskIds.includes(task.id))
        },

        // Async operations (placeholders)
        fetchTasks: async () => {
          set({ loading: true, error: null })
          try {
            // Will be implemented with IPC
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch tasks',
              loading: false
            })
          }
        },

        createTask: async (data) => {
          set({ loading: true, error: null })
          try {
            const result = await window.electronAPI.database.createTask({
              content: data.content,
              description: data.description,
              priority: data.priority,
              deadline: data.deadline,
              projectId: data.projectId,
              areaId: data.areaId,
              parentId: data.parentId,
              sourceResourceId: data.sourceResourceId,
              sourceText: data.sourceText,
              sourceContext: data.sourceContext
            })

            if (result.success) {
              // Add the created task to the store
              set((state) => ({
                tasks: [result.data, ...state.tasks],
                loading: false
              }))
            } else {
              throw new Error(result.error || 'Failed to create task')
            }
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to create task',
              loading: false
            })
            throw error
          }
        },

        fetchTags: async () => {
          set({ loading: true, error: null })
          try {
            // Will be implemented with IPC
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch tags',
              loading: false
            })
          }
        }
      }),
      {
        name: 'task-store',
        partialize: (state) => ({
          tasks: state.tasks,
          tags: state.tags,
          taskTags: state.taskTags,
          checklists: state.checklists,
          checklistInstances: state.checklistInstances,
          currentTask: state.currentTask,
          filter: state.filter
        })
      }
    ),
    {
      name: 'task-store'
    }
  )
)
