import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Badge } from '../ui/badge'
import { Calendar, TrendingUp, Target, Plus, MoreHorizontal, Edit, Trash2 } from 'lucide-react'
import { cn } from '../../lib/utils'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
// {{ AURA-X: Add - 导入双向KPI计算函数. Approval: 寸止(ID:1738157400). }}
import { calculateMetricProgress, getMetricDirection } from '../../lib/areaMetricAdapter'
import type { AreaMetric, AreaMetricRecord } from '../../../../shared/types'

interface AreaMetricQuickInputProps {
  metric: AreaMetric
  onRecordCreated?: (record: AreaMetricRecord) => void
  onEdit?: (metric: AreaMetric) => void
  onDelete?: (metric: AreaMetric) => void
  className?: string
}

export function AreaMetricQuickInput({ 
  metric, 
  onRecordCreated, 
  onEdit, 
  onDelete, 
  className 
}: AreaMetricQuickInputProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [value, setValue] = useState('')
  const [note, setNote] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { addNotification } = useUIStore()

  // {{ AURA-X: Modify - 使用双向KPI进度计算. Approval: 寸止(ID:1738157400). }}
  // 计算进度百分比，支持增长型和减少型指标
  const getProgress = () => {
    if (!metric.target) return null
    return calculateMetricProgress(metric)
  }

  // 获取指标方向
  const getDirection = () => {
    return getMetricDirection(metric)
  }

  // 获取状态颜色
  const getStatusColor = () => {
    const progress = getProgress()
    if (progress === null) return 'text-muted-foreground'
    if (progress >= 100) return 'text-green-600'
    if (progress >= 75) return 'text-blue-600'
    if (progress >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!value.trim()) {
      addNotification({
        type: 'error',
        title: 'Invalid Input',
        message: 'Please enter a value'
      })
      return
    }

    setIsSubmitting(true)
    try {
      const result = await databaseApi.createAreaMetricRecord({
        metricId: metric.id,
        value: value.trim(),
        note: note.trim() || undefined
      })

      if (result.success) {
        addNotification({
          type: 'success',
          title: 'Record Added',
          message: `New value recorded for ${metric.name}`
        })
        
        if (onRecordCreated) {
          onRecordCreated(result.data)
        }
        
        setIsOpen(false)
        setValue('')
        setNote('')
      } else {
        throw new Error(result.error || 'Failed to create record')
      }
    } catch (error) {
      console.error('Failed to create area metric record:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Record',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEdit = () => {
    if (onEdit) {
      onEdit(metric)
    }
  }

  const handleDelete = () => {
    if (onDelete) {
      onDelete(metric)
    }
  }

  const progress = getProgress()

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className={cn(
          'group relative p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md hover:border-primary/50',
          className
        )}>
          {/* Metric Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-sm truncate">{metric.name}</h3>
              <div className="flex items-center gap-2 mt-1">
                <span className={cn('text-lg font-bold', getStatusColor())}>
                  {metric.value}
                </span>
                {metric.unit && (
                  <span className="text-xs text-muted-foreground">{metric.unit}</span>
                )}
                {metric.target && (
                  <span className="text-xs text-muted-foreground">
                    {/* {{ AURA-X: Add - 显示方向指示器. Approval: 寸止(ID:1738157400). }} */}
                    {getDirection() === 'decrease' ? '→' : '/'} {metric.target}
                  </span>
                )}
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Button
                size="sm"
                variant="ghost"
                className="opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <Plus className="h-4 w-4" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Metric
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDelete} className="text-destructive">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Metric
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Progress Bar */}
          {progress !== null && (
            <div className="mb-3">
              <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                <span>Progress</span>
                <span>{progress.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className={cn(
                    'h-2 rounded-full transition-all',
                    progress >= 100 ? 'bg-green-500' :
                    progress >= 75 ? 'bg-blue-500' :
                    progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                  )}
                  style={{ width: `${Math.min(progress, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              <span>Quick Record</span>
            </div>
            {metric.target && (
              <Badge variant="outline" className="text-xs">
                <Target className="h-3 w-3 mr-1" />
                Target: {metric.target}
              </Badge>
            )}
            {metric.frequency && (
              <Badge variant="secondary" className="text-xs">
                {metric.frequency}
              </Badge>
            )}
          </div>
        </div>
      </DialogTrigger>

      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Record New Value
          </DialogTitle>
          <DialogDescription>
            Add a new data point for <strong>{metric.name}</strong>
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="value">
              Value {metric.unit && `(${metric.unit})`}
            </Label>
            <Input
              id="value"
              type="text"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder={`Enter new value${metric.unit ? ` in ${metric.unit}` : ''}`}
              autoFocus
            />
            {metric.target && (
              <div className="text-xs text-muted-foreground">
                Current: {metric.value} | Target: {metric.target}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="note">Note (Optional)</Label>
            <Textarea
              id="note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="Add a note about this record..."
              rows={3}
            />
          </div>

          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Calendar className="h-3 w-3" />
            <span>Recorded at: {new Date().toLocaleString()}</span>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Recording...' : 'Record Value'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default AreaMetricQuickInput
