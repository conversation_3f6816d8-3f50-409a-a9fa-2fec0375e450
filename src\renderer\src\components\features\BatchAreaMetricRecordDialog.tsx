import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { ScrollArea } from '../ui/scroll-area'
import { Calendar, Save, X } from 'lucide-react'
import { cn } from '../../lib/utils'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import type { AreaMetric, AreaMetricRecord } from '../../../../shared/types'

interface BatchAreaMetricRecordDialogProps {
  isOpen: boolean
  onClose: () => void
  metrics: AreaMetric[]
  onRecordsCreated?: (records: AreaMetricRecord[]) => void
}

interface MetricRecordInput {
  metricId: string
  value: string
  note: string
  enabled: boolean
}

export function BatchAreaMetricRecordDialog({ 
  isOpen, 
  onClose, 
  metrics, 
  onRecordsCreated 
}: BatchAreaMetricRecordDialogProps) {
  const [recordInputs, setRecordInputs] = useState<Record<string, MetricRecordInput>>({})
  const [globalNote, setGlobalNote] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { addNotification } = useUIStore()

  // 初始化记录输入
  const initializeInputs = () => {
    const inputs: Record<string, MetricRecordInput> = {}
    metrics.forEach(metric => {
      inputs[metric.id] = {
        metricId: metric.id,
        value: metric.value, // 使用当前值作为默认值
        note: '',
        enabled: true
      }
    })
    setRecordInputs(inputs)
  }

  // 当对话框打开时初始化输入
  useEffect(() => {
    if (isOpen && metrics.length > 0) {
      initializeInputs()
    }
  }, [isOpen, metrics.length])

  const updateRecordInput = (metricId: string, field: keyof MetricRecordInput, value: any) => {
    setRecordInputs(prev => ({
      ...prev,
      [metricId]: {
        ...prev[metricId],
        [field]: value
      }
    }))
  }

  const handleSubmit = async () => {
    const enabledRecords = Object.values(recordInputs).filter(input => input.enabled)
    
    if (enabledRecords.length === 0) {
      addNotification({
        type: 'error',
        title: 'No Records Selected',
        message: 'Please select at least one metric to record'
      })
      return
    }

    // 验证所有启用的记录都有值
    const invalidRecords = enabledRecords.filter(input => !input.value.trim())
    if (invalidRecords.length > 0) {
      addNotification({
        type: 'error',
        title: 'Invalid Input',
        message: 'Please enter values for all selected metrics'
      })
      return
    }

    setIsSubmitting(true)
    try {
      const createdRecords: AreaMetricRecord[] = []
      
      // 逐个创建记录
      for (const input of enabledRecords) {
        const result = await databaseApi.createAreaMetricRecord({
          metricId: input.metricId,
          value: input.value.trim(),
          note: input.note.trim() || globalNote.trim() || undefined
        })
        
        if (result.success) {
          createdRecords.push(result.data)
        } else {
          throw new Error(`Failed to create record for metric: ${result.error}`)
        }
      }

      addNotification({
        type: 'success',
        title: 'Records Created',
        message: `Successfully recorded ${createdRecords.length} metric values`
      })

      if (onRecordsCreated) {
        onRecordsCreated(createdRecords)
      }

      onClose()
      setRecordInputs({})
      setGlobalNote('')
    } catch (error) {
      console.error('Failed to create batch records:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Create Records',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const enabledCount = Object.values(recordInputs).filter(input => input.enabled).length

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            Batch Record Metric Values
          </DialogTitle>
          <DialogDescription>
            Record values for multiple metrics at once. Select which metrics to update and enter their new values.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Global Note */}
          <div className="space-y-2">
            <Label htmlFor="global-note">Global Note (Optional)</Label>
            <Textarea
              id="global-note"
              value={globalNote}
              onChange={(e) => setGlobalNote(e.target.value)}
              placeholder="Add a note that applies to all records..."
              rows={2}
            />
          </div>

          {/* Metric Records */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Metric Records</Label>
              <div className="text-sm text-muted-foreground">
                {enabledCount} of {metrics.length} selected
              </div>
            </div>
            
            <ScrollArea className="h-[300px] border rounded-md p-2">
              <div className="space-y-3">
                {metrics.map((metric) => {
                  const input = recordInputs[metric.id]
                  if (!input) return null

                  return (
                    <Card key={metric.id} className={cn(
                      'transition-all',
                      input.enabled ? 'border-primary/50 bg-primary/5' : 'opacity-60'
                    )}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-sm font-medium">
                            {metric.name}
                          </CardTitle>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => updateRecordInput(metric.id, 'enabled', !input.enabled)}
                          >
                            {input.enabled ? (
                              <X className="h-4 w-4 text-red-500" />
                            ) : (
                              <span className="text-xs">Enable</span>
                            )}
                          </Button>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Current: {metric.value}{metric.unit && ` ${metric.unit}`}
                          {metric.target && ` | Target: ${metric.target}`}
                        </div>
                      </CardHeader>
                      
                      {input.enabled && (
                        <CardContent className="pt-0 space-y-2">
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <Label className="text-xs">New Value</Label>
                              <Input
                                value={input.value}
                                onChange={(e) => updateRecordInput(metric.id, 'value', e.target.value)}
                                placeholder={`Enter value${metric.unit ? ` (${metric.unit})` : ''}`}
                                className="h-8"
                              />
                            </div>
                            <div>
                              <Label className="text-xs">Note</Label>
                              <Input
                                value={input.note}
                                onChange={(e) => updateRecordInput(metric.id, 'note', e.target.value)}
                                placeholder="Optional note"
                                className="h-8"
                              />
                            </div>
                          </div>
                        </CardContent>
                      )}
                    </Card>
                  )
                })}
              </div>
            </ScrollArea>
          </div>

          {/* Recording Time */}
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Calendar className="h-3 w-3" />
            <span>All records will be timestamped: {new Date().toLocaleString()}</span>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || enabledCount === 0}
          >
            {isSubmitting ? 'Recording...' : `Record ${enabledCount} Metrics`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default BatchAreaMetricRecordDialog
