import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { cn } from '../../lib/utils'
import { useAreaStore } from '../../store/areaStore'
import { useLanguage } from '../../contexts/LanguageContext'

interface Habit {
  id: string
  name: string
  description?: string
  frequency: 'daily' | 'weekly' | 'monthly'
  target?: number
  unit?: string
  color: string
  areaId?: string
  records: Array<{ date: string; completed: boolean; value?: number; note?: string }>
  createdAt: string
  updatedAt: string
}

interface CreateHabitDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (habitData: Omit<Habit, 'id' | 'createdAt' | 'updatedAt' | 'records'>) => void
  initialData?: Partial<Habit>
  areaId?: string
}

export function CreateHabitDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  areaId
}: CreateHabitDialogProps) {
  const { areas } = useAreaStore()
  const { t, language } = useLanguage()

  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    description: initialData?.description || '',
    frequency: initialData?.frequency || 'daily',
    target: initialData?.target || '',
    unit: initialData?.unit || '',
    color: initialData?.color || '#3b82f6',
    areaId: initialData?.areaId || areaId || 'none'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const frequencyOptions = [
    {
      value: 'daily',
      label: t('pages.habits.dialog.frequencyOptions.daily'),
      description: t('pages.habits.dialog.frequencyDescriptions.daily')
    },
    {
      value: 'weekly',
      label: t('pages.habits.dialog.frequencyOptions.weekly'),
      description: t('pages.habits.dialog.frequencyDescriptions.weekly')
    },
    {
      value: 'monthly',
      label: t('pages.habits.dialog.frequencyOptions.monthly'),
      description: t('pages.habits.dialog.frequencyDescriptions.monthly')
    }
  ]

  const colorOptions = [
    '#3b82f6', // Blue
    '#10b981', // Green
    '#f59e0b', // Yellow
    '#ef4444', // Red
    '#8b5cf6', // Purple
    '#06b6d4', // Cyan
    '#f97316', // Orange
    '#84cc16', // Lime
    '#ec4899', // Pink
    '#6b7280' // Gray
  ]

  // Define habit examples based on current language
  const habitExamples =
    language === 'zh'
      ? [
          {
            category: '🏃 健康与健身',
            examples: ['锻炼30分钟', '喝8杯水', '服用维生素', '走10000步']
          },
          {
            category: '🧠 学习与成长',
            examples: ['阅读30分钟', '练习技能', '学习新词汇', '写日记']
          },
          {
            category: '💼 工作效率',
            examples: ['回顾每日目标', '清理工作区', '计划明天', '查看邮件']
          },
          { category: '🧘 正念冥想', examples: ['冥想10分钟', '练习感恩', '深呼吸', '拉伸'] }
        ]
      : [
          {
            category: '🏃 Health & Fitness',
            examples: [
              'Exercise 30 minutes',
              'Drink 8 glasses of water',
              'Take vitamins',
              'Walk 10,000 steps'
            ]
          },
          {
            category: '🧠 Learning & Growth',
            examples: [
              'Read for 30 minutes',
              'Practice a skill',
              'Learn new vocabulary',
              'Write in journal'
            ]
          },
          {
            category: '💼 Productivity',
            examples: ['Review daily goals', 'Clean workspace', 'Plan tomorrow', 'Check emails']
          },
          {
            category: '🧘 Mindfulness',
            examples: ['Meditate 10 minutes', 'Practice gratitude', 'Deep breathing', 'Stretch']
          }
        ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) return

    setIsSubmitting(true)
    try {
      const habitData: Omit<Habit, 'id' | 'createdAt' | 'updatedAt' | 'records'> = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        frequency: formData.frequency as 'daily' | 'weekly' | 'monthly',
        target: formData.target ? parseInt(String(formData.target)) : undefined,
        unit: formData.unit.trim() || undefined,
        color: formData.color,
        areaId: formData.areaId === 'none' ? undefined : formData.areaId
      }

      await onSubmit(habitData)
      onClose()

      // Reset form
      setFormData({
        name: '',
        description: '',
        frequency: 'daily',
        target: '',
        unit: '',
        color: '#3b82f6',
        areaId: areaId || 'none'
      })
    } catch (error) {
      console.error('Failed to create habit:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div
              className="w-8 h-8 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: `${formData.color}20`, color: formData.color }}
            >
              <span className="font-semibold">H</span>
            </div>
            {initialData
              ? t('pages.habits.dialog.editTitle')
              : t('pages.habits.dialog.createTitle')}
          </DialogTitle>
          <DialogDescription>{t('pages.habits.dialog.description')}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('pages.habits.dialog.habitNameRequired')}</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                placeholder={t('pages.habits.dialog.habitNamePlaceholder')}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">{t('pages.habits.dialog.descriptionLabel')}</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                placeholder={t('pages.habits.dialog.descriptionPlaceholder')}
                rows={2}
              />
            </div>
          </div>

          {/* Frequency and Target */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="frequency">{t('pages.habits.dialog.frequency')}</Label>
              <Select
                value={formData.frequency}
                onValueChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    frequency: value as 'daily' | 'weekly' | 'monthly'
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {frequencyOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="target">{t('pages.habits.dialog.target')}</Label>
              <Input
                id="target"
                type="number"
                min="1"
                value={formData.target}
                onChange={(e) => setFormData((prev) => ({ ...prev, target: e.target.value }))}
                placeholder={t('pages.habits.dialog.targetPlaceholder')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="unit">{t('pages.habits.dialog.unit')}</Label>
              <Input
                id="unit"
                value={formData.unit}
                onChange={(e) => setFormData((prev) => ({ ...prev, unit: e.target.value }))}
                placeholder={t('pages.habits.dialog.unitPlaceholder')}
              />
            </div>
          </div>

          {/* Color and Area */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>{t('pages.habits.dialog.color')}</Label>
              <div className="flex flex-wrap gap-2">
                {colorOptions.map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={cn(
                      'w-8 h-8 rounded-full border-2 transition-all',
                      formData.color === color
                        ? 'border-gray-400 scale-110'
                        : 'border-gray-200 hover:scale-105'
                    )}
                    style={{ backgroundColor: color }}
                    onClick={() => setFormData((prev) => ({ ...prev, color }))}
                  />
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="area">{t('pages.habits.dialog.associatedArea')}</Label>
              <Select
                value={formData.areaId}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, areaId: value }))}
                disabled={!!areaId}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('pages.habits.dialog.selectAreaPlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{t('pages.habits.dialog.noArea')}</SelectItem>
                  {areas
                    .filter((area) => !area.archived)
                    .map((area) => (
                      <SelectItem key={area.id} value={area.id}>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-area"></div>
                          {area.name}
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Habit Examples */}
          {!initialData && (
            <div className="space-y-3">
              <Label>{t('pages.habits.dialog.habitExamples')}</Label>
              <div className="space-y-3">
                {habitExamples.map((category) => (
                  <div key={category.category} className="space-y-2">
                    <div className="text-sm font-medium text-muted-foreground">
                      {category.category}
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {category.examples.map((example) => (
                        <Button
                          key={example}
                          type="button"
                          variant="outline"
                          size="sm"
                          className="h-7 px-2 text-xs"
                          onClick={() => setFormData((prev) => ({ ...prev, name: example }))}
                        >
                          {example}
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              {t('pages.habits.dialog.cancel')}
            </Button>
            <Button type="submit" disabled={!formData.name.trim() || isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>
                    {initialData
                      ? t('pages.habits.dialog.updating')
                      : t('pages.habits.dialog.creating')}
                  </span>
                </div>
              ) : initialData ? (
                t('pages.habits.dialog.update')
              ) : (
                t('pages.habits.dialog.create')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateHabitDialog
