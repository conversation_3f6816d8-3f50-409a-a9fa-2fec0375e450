/**
 * 领域标准监控组件
 * 专门用于监控和评估领域标准的达成情况
 */

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Target, 
  TrendingUp,
  AlertTriangle,
  Award,
  Calendar
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import type { AreaMetric, AreaMetricRecord } from '../../../../shared/types'

interface AreaStandardMonitorProps {
  areaId: string
  className?: string
}

interface StandardMetric extends AreaMetric {
  trackingType: 'standard'
  standardConfig: {
    criteria: string[]
    passingScore: number
    evaluationPeriod: string
  }
}

interface StandardEvaluation {
  metric: StandardMetric
  currentScore: number
  status: 'passing' | 'failing' | 'warning' | 'pending'
  lastEvaluation: Date | null
  nextEvaluation: Date
  criteriaStatus: { [criterion: string]: boolean }
  trend: 'up' | 'down' | 'stable'
  recommendations: string[]
}

export function AreaStandardMonitor({ areaId, className }: AreaStandardMonitorProps) {
  const [standardMetrics, setStandardMetrics] = useState<StandardMetric[]>([])
  const [evaluations, setEvaluations] = useState<StandardEvaluation[]>([])
  const [loading, setLoading] = useState(true)
  const { addNotification } = useUIStore()

  useEffect(() => {
    loadStandardMetrics()
  }, [areaId])

  const loadStandardMetrics = async () => {
    setLoading(true)
    try {
      const result = await databaseApi.getAreaMetrics(areaId)
      if (result.success) {
        const allMetrics = result.data || []
        const standards = allMetrics.filter(
          (metric: any) => metric.trackingType === 'standard' && metric.standardConfig
        ) as StandardMetric[]
        
        setStandardMetrics(standards)
        await evaluateStandards(standards)
      }
    } catch (error) {
      console.error('Failed to load standard metrics:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Load Standards',
        message: 'Could not load area standards'
      })
    } finally {
      setLoading(false)
    }
  }

  const evaluateStandards = async (standards: StandardMetric[]) => {
    const evaluationPromises = standards.map(async (metric) => {
      try {
        const recordsResult = await databaseApi.getAreaMetricRecords(metric.id, 50)
        const records = recordsResult.success ? recordsResult.data || [] : []
        
        return evaluateStandard(metric, records)
      } catch (error) {
        console.error(`Failed to evaluate standard ${metric.name}:`, error)
        return createDefaultEvaluation(metric)
      }
    })

    const evaluationResults = await Promise.all(evaluationPromises)
    setEvaluations(evaluationResults)
  }

  const evaluateStandard = (metric: StandardMetric, records: AreaMetricRecord[]): StandardEvaluation => {
    const config = metric.standardConfig
    const now = new Date()
    
    // 计算评估周期
    const periodDays = config.evaluationPeriod === 'weekly' ? 7 : 
                      config.evaluationPeriod === 'monthly' ? 30 : 90
    
    const periodStart = new Date(now)
    periodStart.setDate(now.getDate() - periodDays)
    
    // 获取当前周期的记录
    const currentPeriodRecords = records.filter(record => 
      new Date(record.recordedAt) >= periodStart
    )

    // 计算当前分数（基于记录数量和质量）
    let currentScore = 0
    if (currentPeriodRecords.length > 0) {
      const avgValue = currentPeriodRecords.reduce((sum, record) => 
        sum + (parseFloat(record.value) || 0), 0
      ) / currentPeriodRecords.length
      
      const target = parseFloat(metric.target || '100')
      currentScore = Math.min((avgValue / target) * 100, 100)
    }

    // 确定状态
    let status: StandardEvaluation['status']
    if (currentScore >= config.passingScore) {
      status = 'passing'
    } else if (currentScore >= config.passingScore * 0.8) {
      status = 'warning'
    } else if (currentPeriodRecords.length === 0) {
      status = 'pending'
    } else {
      status = 'failing'
    }

    // 计算趋势
    const previousPeriodStart = new Date(periodStart)
    previousPeriodStart.setDate(periodStart.getDate() - periodDays)
    
    const previousPeriodRecords = records.filter(record => {
      const recordDate = new Date(record.recordedAt)
      return recordDate >= previousPeriodStart && recordDate < periodStart
    })

    let trend: StandardEvaluation['trend'] = 'stable'
    if (previousPeriodRecords.length > 0) {
      const prevAvg = previousPeriodRecords.reduce((sum, record) => 
        sum + (parseFloat(record.value) || 0), 0
      ) / previousPeriodRecords.length
      
      const currentAvg = currentPeriodRecords.length > 0 ? 
        currentPeriodRecords.reduce((sum, record) => 
          sum + (parseFloat(record.value) || 0), 0
        ) / currentPeriodRecords.length : 0

      if (currentAvg > prevAvg * 1.1) trend = 'up'
      else if (currentAvg < prevAvg * 0.9) trend = 'down'
    }

    // 生成建议
    const recommendations: string[] = []
    if (status === 'failing') {
      recommendations.push('Immediate action required to meet standard criteria')
      recommendations.push('Review and adjust current processes')
    } else if (status === 'warning') {
      recommendations.push('Monitor closely to prevent decline')
      recommendations.push('Consider process improvements')
    } else if (status === 'pending') {
      recommendations.push('Start tracking to establish baseline')
    }

    // 计算下次评估时间
    const nextEvaluation = new Date(now)
    nextEvaluation.setDate(now.getDate() + periodDays)

    // 模拟标准状态（实际应用中可能需要更复杂的逻辑）
    const criteriaStatus: { [criterion: string]: boolean } = {}
    config.criteria.forEach((criterion, index) => {
      criteriaStatus[criterion] = currentScore >= config.passingScore * (0.8 + index * 0.1)
    })

    return {
      metric,
      currentScore: Math.round(currentScore),
      status,
      lastEvaluation: currentPeriodRecords.length > 0 ? 
        new Date(Math.max(...currentPeriodRecords.map(r => new Date(r.recordedAt).getTime()))) : null,
      nextEvaluation,
      criteriaStatus,
      trend,
      recommendations
    }
  }

  const createDefaultEvaluation = (metric: StandardMetric): StandardEvaluation => {
    const criteriaStatus: { [criterion: string]: boolean } = {}
    metric.standardConfig.criteria.forEach(criterion => {
      criteriaStatus[criterion] = false
    })

    return {
      metric,
      currentScore: 0,
      status: 'pending',
      lastEvaluation: null,
      nextEvaluation: new Date(),
      criteriaStatus,
      trend: 'stable',
      recommendations: ['Start tracking to establish baseline']
    }
  }

  const getStatusColor = (status: StandardEvaluation['status']) => {
    switch (status) {
      case 'passing': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      case 'failing': return 'text-red-600 bg-red-100'
      case 'pending': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: StandardEvaluation['status']) => {
    switch (status) {
      case 'passing': return <CheckCircle className="h-4 w-4" />
      case 'warning': return <AlertTriangle className="h-4 w-4" />
      case 'failing': return <XCircle className="h-4 w-4" />
      case 'pending': return <Clock className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getTrendIcon = (trend: StandardEvaluation['trend']) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down': return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />
      case 'stable': return <Target className="h-4 w-4 text-gray-600" />
      default: return <Target className="h-4 w-4 text-gray-600" />
    }
  }

  const overallStats = useMemo(() => {
    const total = evaluations.length
    const passing = evaluations.filter(e => e.status === 'passing').length
    const warning = evaluations.filter(e => e.status === 'warning').length
    const failing = evaluations.filter(e => e.status === 'failing').length
    const pending = evaluations.filter(e => e.status === 'pending').length

    return { total, passing, warning, failing, pending }
  }, [evaluations])

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <Clock className="h-8 w-8 mx-auto mb-2 animate-pulse" />
          <p>Evaluating area standards...</p>
        </CardContent>
      </Card>
    )
  }

  if (standardMetrics.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8 text-muted-foreground">
          <Target className="h-8 w-8 mx-auto mb-2" />
          <p className="text-sm">No area standards defined</p>
          <p className="text-xs mt-1">Create standards to monitor area quality</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Area Standards Monitor
          </CardTitle>
          <CardDescription>
            Track compliance and performance against area standards
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* 总览统计 */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{overallStats.total}</div>
                <p className="text-sm text-muted-foreground">Total Standards</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{overallStats.passing}</div>
                <p className="text-sm text-muted-foreground">Passing</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-yellow-600">{overallStats.warning}</div>
                <p className="text-sm text-muted-foreground">Warning</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-red-600">{overallStats.failing}</div>
                <p className="text-sm text-muted-foreground">Failing</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-600">{overallStats.pending}</div>
                <p className="text-sm text-muted-foreground">Pending</p>
              </CardContent>
            </Card>
          </div>

          {/* 标准列表 */}
          <div className="space-y-4">
            {evaluations.map((evaluation) => (
              <Card key={evaluation.metric.id} className="border-l-4 border-l-blue-400">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium">{evaluation.metric.name}</h3>
                        <Badge className={cn('text-xs', getStatusColor(evaluation.status))}>
                          {getStatusIcon(evaluation.status)}
                          <span className="ml-1 capitalize">{evaluation.status}</span>
                        </Badge>
                        {getTrendIcon(evaluation.trend)}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {evaluation.metric.description || 'No description available'}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">{evaluation.currentScore}%</div>
                      <p className="text-xs text-muted-foreground">
                        Target: {evaluation.metric.standardConfig.passingScore}%
                      </p>
                    </div>
                  </div>

                  {/* 进度条 */}
                  <div className="mb-3">
                    <Progress 
                      value={evaluation.currentScore} 
                      className="h-2"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>0%</span>
                      <span>Passing: {evaluation.metric.standardConfig.passingScore}%</span>
                      <span>100%</span>
                    </div>
                  </div>

                  {/* 标准详情 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2">Criteria Status</h4>
                      <div className="space-y-1">
                        {evaluation.metric.standardConfig.criteria.map((criterion) => (
                          <div key={criterion} className="flex items-center gap-2 text-sm">
                            {evaluation.criteriaStatus[criterion] ? (
                              <CheckCircle className="h-3 w-3 text-green-600" />
                            ) : (
                              <XCircle className="h-3 w-3 text-red-600" />
                            )}
                            <span className={evaluation.criteriaStatus[criterion] ? 'text-green-700' : 'text-red-700'}>
                              {criterion}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2">Evaluation Info</h4>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-3 w-3" />
                          <span>
                            Period: {evaluation.metric.standardConfig.evaluationPeriod}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-3 w-3" />
                          <span>
                            Next: {evaluation.nextEvaluation.toLocaleDateString()}
                          </span>
                        </div>
                        {evaluation.lastEvaluation && (
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-3 w-3" />
                            <span>
                              Last: {evaluation.lastEvaluation.toLocaleDateString()}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 建议 */}
                  {evaluation.recommendations.length > 0 && (
                    <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                      <h4 className="text-sm font-medium mb-2">Recommendations</h4>
                      <ul className="text-sm space-y-1">
                        {evaluation.recommendations.map((rec, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-blue-600">•</span>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
